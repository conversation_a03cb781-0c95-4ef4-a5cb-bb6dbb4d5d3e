<template>
    <el-dialog :title="getModeDesc" v-model="dialogVisible" width="500px" :before-close="handleCancel">
        <el-form ref="formRef" :model="pageForm" :rules="formRules" label-width="100px" label-suffix=":">
            <el-form-item label="网站" prop="siteCode">
                <el-select v-model="pageForm.siteCode" placeholder="请选择" size="default" clearable>
                    <el-option v-for="item in siteOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="页面名称" prop="name">
                <el-input v-model="pageForm.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="标签" prop="tags">
                <el-input v-model="pageForm.tags" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="分辨率" prop="resolution">
                <el-select v-model="pageForm.resolution" placeholder="请选择" size="default" clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务分组" prop="bizGroup">
                <el-select v-model="pageForm.bizGroup" placeholder="请选择" size="default" clearable>
                    <el-option v-for="item in bizGroupOption" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="pageForm.orgId"
                    placeholder="请选择"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref } from 'vue';
    import type { FormInstance } from 'element-plus';
    import { Dimension, Page } from '@smartdesk/common/types';
    import { dimensionApi, siteApi } from '@smartdesk/common/api';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { nameDuplicateValidator } from '@smartdesk/common/utils';

    // 参数
    const props = defineProps<{
        visible: boolean;
        modelValue: Partial<Page>;
        mode: 'create' | 'update' | 'copy';
    }>();

    // 定义 Emits
    const emit = defineEmits(['update:visible', 'submit', 'update:modelValue']);

    // pinia store
    const enumStore = useEnumStore();
    const siteStore = useSiteStore();

    // 分辨率枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 业务分组枚举
    const bizGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('bizGroup') || []);

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    // 网站选项
    const siteOptions = ref<LabelValue[]>([] as LabelValue[]);

    // 表单引用
    const formRef = ref<FormInstance>();

    // 页面表单
    const pageForm = ref<Partial<Page>>({});

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 表单验证规则
    const formRules = {
        siteCode: [
            {
                required: true,
                message: '请选择网站',
                trigger: 'blur',
            },
        ],
        name: [
            {
                required: true,
                message: '请输入页面名称',
                trigger: 'blur',
            },
            {
                validator: nameDuplicateValidator(() => {
                    return {
                        entityType: 'Page',
                        siteCode: pageForm.value.siteCode,
                        name: pageForm.value.name,
                        excludeId: pageForm.value.id,
                    };
                }),
                trigger: ['blur', 'change'],
            },
        ],
        resolution: [
            {
                required: true,
                message: '请选择分辨率',
                trigger: 'blur',
            },
        ],
    };

    // 取消处理
    const handleCancel = () => {
        dialogVisible.value = false;
    };

    // 提交处理
    const handleSubmit = async () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', { ...pageForm.value });
                emit('update:modelValue', undefined);
                dialogVisible.value = false;
            }
        });
    };

    // 获取站点列表
    const getSiteList = async () => {
        const res = await siteApi.optionsSite();
        siteOptions.value = res.result;
    };

    // 重置表单
    const resetForm = () => {
        pageForm.value = {
            name: '',
            resolution: '',
            siteCode: siteStore.currentSiteCode,
        };
        formRef.value?.clearValidate();
    };

    // 初始化表单
    const initForm = () => {
        if (props.modelValue?.id) {
            pageForm.value = { ...props.modelValue };
        } else {
            resetForm();
        }
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 获取 mode 描述
    const getModeDesc = computed(() => {
        switch (props.mode) {
            case 'create':
                return '新增页面';
            case 'update':
                return '编辑页面';
            case 'copy':
                return '复制页面';
            default:
                return '';
        }
    });

    // 组件挂载时请求数据
    onMounted(() => {
        getSiteList();
        getOrgTree();
        initForm();
    });
</script>
