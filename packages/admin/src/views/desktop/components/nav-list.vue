<template>
    <el-table
        ref="tableRef"
        :data="navList"
        row-key="code"
        :header-cell-style="{ color: 'black', height: '50px' }"
        :tree-props="{ children: 'children', checkStrictly: true }"
        style="width: 100%"
        :indent="0"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="70" label="全选" />
        <el-table-column label="" width="50">
            <template #default>
                <span style="visibility: hidden"></span>
            </template>
        </el-table-column>
        <el-table-column property="title" label="导航名称" width="240">
            <template #default="{ row, level }">
                <template v-if="isEditing(row)">
                    <el-input v-model="row.title" size="small" style="flex: 1; min-width: 150px" />
                </template>
                <template v-else>
                    <el-tooltip placement="top">
                        <template #content>
                            {{ row.title }}
                            {{ row.children?.length ? '（' + row.children.length + '）' : '' }}
                        </template>
                        {{ row.title }}
                        {{ row.children?.length ? '（' + row.children.length + '）' : '' }}
                    </el-tooltip>
                </template>
            </template>
        </el-table-column>
        <el-table-column property="pageCode" label="页面名称" width="200">
            <template #default="{ row }">
                <template v-if="isEditing(row)">
                    <div style="display: flex; align-items: center">
                        <el-select
                            v-if="!getAddPageNameByCode(row.code)"
                            v-model="row.pageCode"
                            placeholder="请选择页面"
                            size="small"
                            clearable
                            @click="getPageList"
                            style="width: 120px">
                            <el-option
                                v-for="item in pageList"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code" />
                        </el-select>
                        <el-button
                            v-if="!getAddPageNameByCode(row.code)"
                            type="primary"
                            text
                            style="margin-left: 8px"
                            @click="handleSavePage(row)">
                            新增
                        </el-button>
                        <span v-if="getAddPageNameByCode(row.code)">{{ getAddPageNameByCode(row.code) }}</span>
                    </div>
                </template>
                <template v-else>
                    <el-tooltip placement="top">
                        <template #content>
                            {{ pageList.find((item) => item.code === row.pageCode)?.name }}
                        </template>
                        <el-button type="primary" link @click="onClickPage(row)">
                            {{ pageList.find((item) => item.code === row.pageCode)?.name }}
                        </el-button>
                    </el-tooltip>
                </template>
            </template>
        </el-table-column>
        <el-table-column property="orderNo" label="顺序" width="100">
            <template #default="{ row }">
                <el-input-number
                    v-if="isEditing(row)"
                    v-model="row.orderNo"
                    size="small"
                    :min="0"
                    style="width: 100%"
                    placeholder="请输入顺序" />
            </template>
        </el-table-column>
        <el-table-column property="type" label="导航类型" width="100">
            <template #default="{ row }">
                <el-select
                    v-if="isEditing(row)"
                    v-model="row.type"
                    placeholder="请选择导航类型"
                    size="small"
                    clearable
                    style="width: 100%">
                    <el-option
                        v-for="item in enumStore.getNameCodeNumberOptionsByKey('navType')"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
                <div v-else>
                    {{ enumStore.getLabelByKeyAndValue('navType', row.type) }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="menuType" label="菜单类型" width="100">
            <template #default="{ row }">
                <el-select
                    v-if="isEditing(row)"
                    v-model="row.menuType"
                    placeholder="请选择菜单类型"
                    size="small"
                    clearable
                    style="width: 100%">
                    <el-option
                        v-for="item in enumStore.getNameCodeNumberOptionsByKey('menuType')"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
                <div v-else>
                    {{ enumStore.getLabelByKeyAndValue('menuType', row.menuType) }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="defaultPageFlag" label="默认页面" width="100">
            <template #default="{ row }">
                <el-switch v-if="isEditing(row)" v-model="row.defaultPageFlag" />
                <div v-else>{{ row.defaultPageFlag ? '是' : '否' }}</div>
            </template>
        </el-table-column>

        <el-table-column v-for="imageType in navImageTypes" :key="imageType.code" :label="imageType.name" width="180">
            <template #default="{ row }">
                <template v-if="isEditing(row)">
                    <image-editor v-model="row.icons[imageType.code]" />
                </template>
                <template v-else>
                    <el-image
                        v-if="row.icons[imageType.code]"
                        :src="row.icons[imageType.code]"
                        class="h-14"
                        fit="contain"
                        :preview-src-list="[row.icons[imageType.code]]"
                        hide-on-click-modal
                        preview-teleported>
                        <template #error>
                            <image-error-fallback text="图标损坏" />
                        </template>
                    </el-image>
                </template>
            </template>
        </el-table-column>
        <el-table-column property="ruleCode" label="推荐策略" width="180" show-overflow-tooltip>
            <template #default="{ row }">
                <el-button v-if="isEditing(row)" text type="primary" size="small" @click="onClickSelectRule(row)">
                    {{ personalRuleList.find((item: any) => item.code === row.ruleCode)?.name || '选择推荐策略' }}
                </el-button>
                <div v-else>
                    {{ personalRuleList.find((item) => item.code === row.ruleCode)?.name }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="inverted" label="是否反选" width="100">
            <template #default="{ row }">
                <el-switch v-if="isEditing(row)" v-model="row.inverted" />
                <div v-else>{{ row.inverted ? '是' : '否' }}</div>
            </template>
        </el-table-column>
        <el-table-column property="orgId" label="组织" width="180">
            <template #default="{ row }">
                <template v-if="isEditing(row)">
                    <dimension-selector
                        v-model="row.orgId"
                        placeholder="请选择组织"
                        :data="orgTree"
                        label-key="name"
                        value-key="id"
                        width="370" />
                </template>
                <template v-else>
                    <div>
                        {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                    </div>
                </template>
            </template>
        </el-table-column>
        <el-table-column label="可用状态" width="100">
            <template #default="{ row }">
                <el-switch
                    v-if="row.status === 1"
                    :disabled="
                        !canDisable(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.DISABLE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)" />
                <el-switch
                    v-else
                    :disabled="
                        !canEnable(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ENABLE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)" />
            </template>
        </el-table-column>
        <el-table-column label="状态" width="280">
            <template #default="{ row }">
                <status-columns :publishStatus="row" />
            </template>
        </el-table-column>
        <el-table-column label="修改时间" width="180">
            <template #default="{ row }">
                {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
            </template>
        </el-table-column>
        <el-table-column property="modifiedBy" label="修改人" width="100" />
        <el-table-column label="操作" fixed="right" width="420">
            <template #default="{ row }">
                <template v-if="isEditing(row)">
                    <el-button type="primary" text @click="save(row)"> 保存 </el-button>
                    <el-button type="info" text @click="cancel(row)"> 取消 </el-button>
                </template>
                <template v-else>
                    <el-button
                        :disabled="
                            !canEdit(row) ||
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.EDIT, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        type="primary"
                        text
                        @click="onClickEdit(row)">
                        编辑
                    </el-button>
                    <el-button
                        :disabled="
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ADD_CHILD_NAV, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        v-if="row.type === 1"
                        type="primary"
                        text
                        @click="onClickAddChildNav(row)">
                        新增子导航
                    </el-button>
                    <el-button
                        :disabled="
                            !canAudit(row) ||
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.AUDIT, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        type="primary"
                        text
                        @click="onClickPublish(row)">
                        送审
                    </el-button>
                    <el-button
                        :disabled="
                            !canOnline(row) ||
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ONLINE, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        v-if="canOnline(row)"
                        type="primary"
                        text
                        @click="onClickOnline(row)">
                        上线
                    </el-button>
                    <el-button
                        :disabled="
                            !canOffline(row) ||
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.OFFLINE, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        v-if="canOffline(row)"
                        type="warning"
                        text
                        @click="onClickOffline(row)">
                        下线
                    </el-button>
                    <el-button
                        :disabled="
                            !canDelete(row) ||
                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.DELETE, {
                                type: 'org',
                                value: row.orgId,
                            })
                        "
                        type="danger"
                        text
                        @click="onClickDelete(row)">
                        删除
                    </el-button>
                </template>
            </template>
        </el-table-column>
    </el-table>
    <personal-rule-dialog
        v-model="selectedRuleCode"
        v-model:dialog-visible="personalRuleDialogVisible"
        @save="savePersonalRule" />
    <nav-add-page v-model:visible="addPageValue" @confirm="handleAddPage" />
</template>

<script setup lang="ts">
    import { onMounted, PropType, ref, watch } from 'vue';
    import { Nav, NavSearchForm, Page, PersonalRule, Dimension, NavForm } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, navApi, pageApi, personalRuleApi, publishApi } from '@smartdesk/common/api';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { Enumeration, LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@smartdesk/common/permission';
    import { nextTick } from 'vue';
    import NavAddPage from '@smartdesk/admin/views/desktop/components/nav-add-page.vue';
    import { extractDimensions } from '@smartdesk/common/utils';

    const tableRef = ref();
    defineOptions({
        name: 'NavList',
    });

    // 参数
    const props = defineProps({
        sectionForm: {
            type: Object as PropType<Partial<NavSearchForm>>,
            default: () => ({}),
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emits = defineEmits(['update:selection', 'edit', 'addChildNav']);

    // 网站 pinia
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 推荐策略列表
    const personalRuleList = ref<PersonalRule[]>([]);

    // 选中的导航
    const selectedNavs = ref<Nav[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 图片类型
    const navImageTypes = ref<Enumeration[]>(enumStore.getEnumsByKey('navImageTypeEnum') || []);

    const handleSelectionChange = (val: Nav[]) => {
        selectedNavs.value = val;
        emits('update:selection', val);
    };

    // 启用/禁用导航
    const handleStatusChange = async (row: Nav) => {
        const word = row.status === 0 ? '禁用' : '启用';
        if (await feedback.confirm(`确定要${word}该导航吗？`, '确定操作', 'warning')) {
            const response = row.status === 0 ? await navApi.disableNav(row.code) : await navApi.enableNav(row.code);
            if (response.code === 200) {
                feedback.success(`${word}导航成功`);
                await getNavTree();
            } else {
                feedback.error(`${word}导航失败：` + response.msg);
            }
        } else {
            row.status = row.status === 1 ? 0 : 1;
        }
    };

    // 点击页面
    const onClickPage = (row: Nav) => {
        jumpToDesigner('page', row.pageCode, {
            desktopCode: row.ownerPageCode,
            navCode: row.code,
            bizGroup: props.desktop?.bizGroup,
        });
    };

    // 点击删除
    const onClickDelete = async (row: Nav) => {
        if (await feedback.confirm(`确定要删除该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishComplete('Nav', row.code, 'DELETE');
            if (res.code === 200) {
                feedback.success('删除导航成功');
                await getNavTree();
            } else {
                feedback.error('删除导航失败：' + res.msg);
            }
        }
    };

    // 点击送审
    const onClickPublish = async (row: Nav) => {
        if (await feedback.confirm(`确定要送审该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'CREATE');
            if (res.code === 200) {
                feedback.success('送审导航成功');
                await getNavTree();
            } else {
                feedback.error('送审导航失败：' + res.msg);
            }
        }
    };

    // 点击上线
    const onClickOnline = async (row: Nav) => {
        if (await feedback.confirm(`确定要上线该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'ONLINE');
            if (res.code === 200) {
                feedback.success('上线导航成功');
                await getNavTree();
            } else {
                feedback.error('上线导航失败：' + res.msg);
            }
        }
    };

    // 点击下线
    const onClickOffline = async (row: Nav) => {
        if (await feedback.confirm(`确定要下线该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'OFFLINE');
            if (res.code === 200) {
                feedback.success('下线导航成功');
                await getNavTree();
            } else {
                feedback.error('下线导航失败：' + res.msg);
            }
        }
    };

    // 查询导航树
    const getNavTree = async () => {
        try {
            const response = await navApi.findNavTree(props.sectionForm);
            if (response.code === 200) {
                const processTree = (nodes: Nav[]) => {
                    nodes.forEach((node: any) => {
                        node.children = node.children || [];
                        if (node.children.length > 0) {
                            processTree(node.children);
                        }
                    });
                };

                navList.value = response.result;
                processTree(navList.value);
            } else {
                feedback.error('获取导航树失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('获取导航树失败');
        }
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList({ siteCode: siteStore.currentSiteCode }, { paged: false });
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    // 查询推荐策略列表
    const getPersonalRuleList = async () => {
        const res = await personalRuleApi.getPersonalRuleList({}, { paged: false });
        if (res.code === 200) {
            personalRuleList.value = res.result;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // -------------------------------------

    // 用 Set 存储“正在编辑的行”的唯一标识，比如 id
    const editingRows = ref(new Set());

    // 备份原始值
    const rowBackups = new Map();

    // 推荐策略弹窗逻辑同前
    const selectedRuleCode = ref<string>();
    const personalRuleDialogVisible = ref<boolean>(false);
    const editingRuleRow = ref<any>(null);

    // 点击选择推荐策略
    const onClickSelectRule = (row: any) => {
        selectedRuleCode.value = row.ruleCode;
        editingRuleRow.value = row;
        personalRuleDialogVisible.value = true;
    };

    const savePersonalRule = (code: string) => {
        if (editingRuleRow.value) {
            editingRuleRow.value.ruleCode = code;
        }
        personalRuleDialogVisible.value = false;
    };

    // 检查行是否正在编辑
    const isEditing = (row: Nav) => {
        return editingRows.value.has(row.code);
    };

    // 编辑行
    const onClickEdit = (row: Nav) => {
        editingRows.value.add(row.code);
        rowBackups.set(row.code, { ...row }); // 备份一份
    };

    // 保存行
    const save = (row: Nav) => {
        if (!validateNav(row)) return;
        editingRows.value.delete(row.code);
        rowBackups.delete(row.code);
        let navForm = { ...row, page: defaultPage };
        const page = codePageMap.value.get(row.code);
        if (page) {
            navForm.page = page;
            navForm.pageCode = '';
        }
        handleNavSubmit(navForm);
    };

    // 处理导航提交事件
    const handleNavSubmit = async (navForm: Partial<NavForm>) => {
        codePageMap.value.delete(navForm.code || '');
        if (navForm.id) {
            // 走更新
            const res = await navApi.updateNav(navForm.code || '', navForm);
            if (res.code === 200) {
                await refreshNavList();
                feedback.success('更新导航成功');
            } else {
                feedback.error('更新导航失败');
            }
        } else {
            navForm.code = '';
            // 走新增
            const res = await navApi.createNav(navForm);
            if (res.code === 200) {
                await refreshNavList();
                feedback.success('新增导航成功');
            } else {
                feedback.error('新增导航失败');
            }
        }
    };

    // 校验函数
    const validateNav = (row: Nav): boolean => {
        if (!row.title || !row.title.trim()) {
            feedback.error('导航标题不能为空');
            return false;
        }
        if (row.type === undefined || row.type === null) {
            feedback.error('导航类型不能为空');
            return false;
        }
        if (row.menuType === undefined || row.menuType === null) {
            feedback.error('菜单类型不能为空');
            return false;
        }
        // if (!row.pageCode || !row.pageCode.trim()) {
        //     feedback.error('页面名称不能为空');
        //     return false;
        // }
        if (row.orderNo === undefined || row.orderNo === null) {
            feedback.error('顺序不能为空');
            return false;
        }
        return true;
    };

    // 取消操作
    const cancel = (row: Nav) => {
        const backup = rowBackups.get(row.code);
        if (backup) {
            Object.assign(row, backup);
        }
        editingRows.value.delete(row.code);
        rowBackups.delete(row.code);
        if (!row.id) {
            handleUnsavedNavDeletion(row);
        }
        codePageMap.value.delete(row.code);
    };
    // 新增页面弹窗
    const addPageValue = ref<boolean>(false);

    // 单前新增导航表单
    const currentNavForm = ref<NavForm>({} as NavForm);

    // 默认页面
    const defaultPage: Partial<Page> = {
        siteCode: siteStore.currentSite?.code,
        name: '',
        resolution: '',
    };

    const getAddPageNameByCode = (code: string) => {
        return codePageMap.value.get(code)?.name;
    };

    // 点击新增页面按钮
    const handleSavePage = (row: Nav) => {
        currentNavForm.value = { ...row, page: defaultPage };
        addPageValue.value = true;
    };

    // 新增页面弹窗确定
    const handleAddPage = (data: Page) => {
        // 可执行保存逻辑
        currentNavForm.value.page = data;

        if (currentNavForm.value.page) {
            const rect = extractDimensions(currentNavForm.value.page.resolution ?? '');
            currentNavForm.value.page.layout = {
                component: 'page',
                rect: {
                    top: 0,
                    left: 0,
                    width: rect ? rect.width : 0,
                    height: rect ? rect.height : 0,
                },
            } as any;
        }
        onAddPageConfirm();
    };

    const codePageMap = ref(new Map<string, Page>());

    // 确定新增页面
    const onAddPageConfirm = () => {
        // 存储到 Map 中
        codePageMap.value.set(currentNavForm.value.code, currentNavForm.value.page as Page);
    };

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 未保存导航的删除逻辑（无id的情况）
    const handleUnsavedNavDeletion = async (row: Nav) => {
        // 查找父导航（若不存在则使用空父对象）
        const parentNavItem = navList.value.find((item) => item.code === row.parentCode) ?? { children: [] };

        // 从父级children中移除当前导航（适用于有父级的场景）
        if (parentNavItem.children?.some((child) => child.code === row.code)) {
            parentNavItem.children = parentNavItem.children.filter((child) => child.code !== row.code);
            feedback.success('已移除未保存子导航');
            return;
        }

        // 无父级时从根列表移除（适用于顶级未保存导航）
        if (navList.value.some((item) => item.code === row.code)) {
            navList.value = navList.value.filter((item) => item.code !== row.code);
            feedback.success('已移除未保存导航');
            return;
        }
    };

    // 新增一级导航
    const addBaseNav = () => {
        // 生成临时编码并创建顶级导航对象
        const tempCode = generateTempCode();
        const newNav = createBaseNav(
            tempCode,
            props.sectionForm.siteCode || '',
            props.desktop.code,
            { title: '新导航' } // 顶级导航默认标题
        );

        // 响应式添加至导航列表（假设navList是ref或响应式数组）
        navList.value.push(newNav);
        onClickEdit(newNav);
    };
    // 新增子导航
    const onClickAddChildNav = (parentRow: Nav) => {
        // 防御性校验：父导航必须存在且有效
        if (!parentRow?.id || !parentRow.siteId || !parentRow.siteCode) {
            feedback.error('无效的父导航，无法添加子导航');
            return;
        }

        // 生成临时编码并创建子导航对象
        const tempCode = generateTempCode();
        const newChild = createBaseNav(
            tempCode,
            parentRow.siteCode, // 继承父导航站点编码
            props.desktop.code,
            {
                parentId: parentRow.id!, // 父导航ID（非空断言，已通过校验）
                parentCode: parentRow.code!, // 父导航编码（非空断言）
                siteId: parentRow.siteId!, // 继承父导航站点ID（非空断言）
                title: '新子导航', // 子导航默认标题
            }
        );

        // 确保父导航children为响应式数组（兼容初始无children的情况）
        if (!parentRow.children) {
            parentRow.children = [] as Nav[]; // 类型断言（根据实际Nav类型调整）
        }

        // 响应式添加至父导航子列表
        parentRow.children.push(newChild);
        onClickEdit(newChild);
        nextTick(() => {
            tableRef.value.toggleRowExpansion(parentRow, true); // 强制展开父行
        });
    };
    /**
     * 生成临时唯一编码（用于未保存的导航）
     */
    const generateTempCode = (): string => {
        return `temp_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
    };
    /**
     * 创建基础导航对象（公共属性）
     * @param code 导航编码（临时或正式）
     * @param siteCode 站点编码
     * @param ownerPageCode 所属页面编码
     * @param extra 额外属性（如父级信息）
     */
    const createBaseNav = (code: string, siteCode: string, ownerPageCode: string, extra: Partial<Nav> = {}): Nav =>
        ({
            code,
            siteCode,
            ownerPageCode,
            icons: {},
            title: '新导航',
            status: 1,
            ...extra,
        }) as Nav;

    const refreshNavList = async () => {
        await getPageList();
        await getNavTree();
    };
    // -------------------------------------

    watch(
        () => props.sectionForm,
        (newVal) => {
            // 只有网站编码、桌面编码都存在时，才查询导航列表
            if (newVal.siteCode && newVal.desktopCode) {
                getNavTree();
            }
        },
        { deep: true, immediate: true }
    );

    watch(
        () => siteStore.currentSiteCode,
        () => {
            getPageList();
            getPersonalRuleList();
        },
        { deep: true, immediate: true }
    );

    onMounted(() => {
        getOrgOptions();
        getOrgTree();
    });

    defineExpose({
        getNavTree,
        getPageList,
        selectedNavs,
        addBaseNav,
    });
</script>
