<template>
    <PageContainer :showToolbars="false">
        <template #search>
            <desktop-top v-model="desktopSearchForm" @search="handleDesktopSearch" />
        </template>
        <template #list>
            <desktop-list
                :desktop-list="desktopList"
                @design="handleDesktopDesign"
                @nav="handleDesktopNav"
                @nav-group="handleDesktopNavGroup"
                @publish="handleDesktopPublish"
                @delete="handleDesktopDelete"
                @online="handleDesktopOnline"
                @offline="handleDesktopOffline"
                @enable="handleDesktopEnable"
                @disable="handleDesktopDisable"
                @edit="handleDesktopEdit"
                @add="handleDesktopAdd" />
        </template>
    </PageContainer>

    <desktop-nav-drawer
        ref="desktopNavDrawerRef"
        v-model="desktopNavDrawerDialogVisible"
        :desktop="currentDesktop"
        @add-nav="handleNavAdd"
        @edit-nav="handleNavEdit"
        @add-child-nav="handleNavAddChild" />

    <desktop-nav-group-drawer
        ref="desktopNavGroupDrawerRef"
        v-model="desktopNavGroupDrawerDialogVisible"
        :desktop="currentDesktop"
        @add-nav-group="handleNavGroupAdd"
        @edit-nav-group="handleNavGroupEdit" />

    <desktop-add
        v-if="desktopAddDialogVisible"
        v-model:model-value="desktopAddDialogVisible"
        v-model:desktop="desktopForm"
        @on-click-confirm="handleConfirmDesktopAdd"
        @on-click-cancel="handleDesktopSearch" />

    <nav-add
        v-if="navDialogVisible"
        v-model="navDialogVisible"
        :parent-nav="currentParentNav"
        :nav="currentNav"
        :desktop="currentDesktop"
        @submit="handleNavSubmit" />

    <nav-group-dialog
        v-if="navGroupDialogVisible"
        v-model="navGroupDialogVisible"
        :nav-group="currentNavGroup"
        :desktop="currentDesktop"
        @submit="handleNavGroupSubmit" />
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { desktopApi, navApi, navGroupApi, publishApi } from '@smartdesk/common/api';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { DesktopSearchForm, Nav, NavForm, NavGroup, Page } from '@smartdesk/common/types';
    import { extractDimensions } from '@smartdesk/common/utils';
    import NavAdd from './components/nav-add.vue';
    import DesktopTop from './components/desktop-top.vue';
    import DesktopList from './components/desktop-list.vue';
    import DesktopNavDrawer from './components/desktop-nav-drawer.vue';
    import DesktopAdd from './components/desktop-add.vue';
    import DesktopNavGroupDrawer from './components/desktop-nav-group-drawer.vue';
    import NavGroupDialog from './components/nav-group-dialog.vue';
    import PageContainer from '../common/page-container.vue';

    defineOptions({
        name: 'Desktop',
    });

    // 网站 store
    const siteStore = useSiteStore();
    const feedback = useFeedback();

    // 跳转设计器
    const { jumpToDesigner } = useJumpToDesigner();

    // 桌面导航抽屉引用
    const desktopNavDrawerRef = ref<HTMLElement | null>(null);

    // 桌面导航分组抽屉引用
    const desktopNavGroupDrawerRef = ref<HTMLElement | null>(null);

    // 当前桌面
    const currentDesktop = ref<Page>({} as Page);

    // 导航模态框
    const navDialogVisible = ref<boolean>(false);

    // 导航分组模态框
    const navGroupDialogVisible = ref<boolean>(false);

    // 新增桌面模态框
    const desktopAddDialogVisible = ref<boolean>(false);

    // 桌面导航抽屉显隐
    const desktopNavDrawerDialogVisible = ref<boolean>(false);

    // 桌面导航分组抽屉显隐
    const desktopNavGroupDrawerDialogVisible = ref<boolean>(false);

    // 当前导航分组
    const currentNavGroup = ref<Partial<NavGroup>>({});

    // 当前导航
    const currentNav = ref<Partial<Nav>>({});

    // 当前父导航
    const currentParentNav = ref<Partial<Nav>>({});

    // 桌面列表
    const desktopList = ref<Page[]>([]);

    // 桌面查询表单
    const desktopSearchForm = ref<Partial<DesktopSearchForm>>({
        siteCode: siteStore.currentSiteCode,
        tags: '',
        bizGroups: [],
        orgIds: [],
        name: '',
        statuses: [],
        onlineStatuses: [],
        auditStatuses: [],
        visibleStatuses: [],
        delFlags: [0] as number[],
    });

    // 桌面表单
    const desktopForm = ref<Partial<Page>>({
        siteCode: siteStore.currentSiteCode,
    });

    // 处理桌面查询
    const handleDesktopSearch = async () => {
        const searchForm = { ...desktopSearchForm.value };
        if (searchForm.tagsList && searchForm.tagsList.indexOf('-1') !== -1) {
            //  如果分组里含有 -1，去掉该属性
            delete searchForm.tagsList;
        }
        const res = await desktopApi.getDesktopList(searchForm, {
            paged: false,
        });

        if (res.code === 200) {
            desktopList.value = res.result;
        }
    };

    // 处理桌面设计
    const handleDesktopDesign = (desktop: Page): void => {
        jumpToDesigner('desktop', desktop.code);
    };

    // 处理桌面导航
    const handleDesktopNav = (desktop: Page): void => {
        currentDesktop.value = desktop;
        desktopNavDrawerDialogVisible.value = true;

        (desktopNavDrawerRef.value as any)?.openDrawer();
    };

    // 处理桌面导航分组
    const handleDesktopNavGroup = (desktop: Page): void => {
        currentDesktop.value = desktop;
        desktopNavGroupDrawerDialogVisible.value = true;

        (desktopNavGroupDrawerRef.value as any)?.openDrawer();
    };

    // 处理桌面送审
    const handleDesktopPublish = async (desktop: Page) => {
        const res = await publishApi.publishSelf('Desktop', desktop.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('桌面送审流程启动成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面送审失败');
        }
    };

    // 处理桌面删除
    const handleDesktopDelete = async (desktop: Page) => {
        const res = await publishApi.publishComplete('Desktop', desktop.code, 'DELETE');
        if (res.code === 200) {
            feedback.success('桌面删除流程启动成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面删除失败');
        }
    };

    // 处理桌面上线
    const handleDesktopOnline = async (desktop: Page) => {
        const res = await publishApi.publishSelf('Desktop', desktop.code, 'ONLINE');
        if (res.code === 200) {
            feedback.success('桌面上线流程启动成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面上线失败');
        }
    };

    // 处理桌面下线
    const handleDesktopOffline = async (desktop: Page) => {
        const res = await publishApi.publishSelf('Desktop', desktop.code, 'OFFLINE');
        if (res.code === 200) {
            feedback.success('桌面下线流程启动成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面下线失败');
        }
    };

    // 处理桌面启用
    const handleDesktopEnable = async (desktop: Page) => {
        const res = await publishApi.publishSelf('Desktop', desktop.code, 'OFFLINE');
        if (res.code === 200) {
            feedback.success('桌面启用成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面启用失败');
        }
    };

    // 处理桌面禁用
    const handleDesktopDisable = async (desktop: Page) => {
        const res = await publishApi.publishSelf('Desktop', desktop.code, 'OFFLINE');
        if (res.code === 200) {
            feedback.success('桌面禁用成功');
            await handleDesktopSearch();
        } else {
            feedback.error('桌面禁用失败');
        }
    };

    // 处理桌面编辑
    const handleDesktopEdit = (desktop: Page): void => {
        desktopForm.value = desktop;
        desktopAddDialogVisible.value = true;
    };

    // 处理桌面新增
    const handleDesktopAdd = (): void => {
        desktopForm.value = {
            siteCode: siteStore.currentSiteCode,
        };
        desktopAddDialogVisible.value = true;
    };

    // 处理确认桌面新增
    const handleConfirmDesktopAdd = async () => {
        if (desktopForm.value.code) {
            // 修改
            const response = await desktopApi.updateDesktop(desktopForm.value.code, desktopForm.value);
            if (response.code === 200) {
                feedback.success('修改桌面成功');
                desktopAddDialogVisible.value = false;

                await handleDesktopSearch();
            } else {
                feedback.error('修改桌面失败：' + response.msg);
            }
        } else {
            // 新增
            // 补充布局信息
            const rect = extractDimensions(desktopForm.value.resolution ?? '');
            desktopForm.value.layout = {
                component: 'desktop',
                rect: {
                    top: 0,
                    left: 0,
                    width: rect ? rect.width : 0,
                    height: rect ? rect.height : 0,
                },
            } as any;
            const response = await desktopApi.createDesktop(desktopForm.value);
            if (response.code === 200) {
                feedback.success('新增桌面成功');
                desktopAddDialogVisible.value = false;

                await handleDesktopSearch();

                // 进入设计器页面
                jumpToDesigner('desktop', response.result.code);
            } else {
                feedback.error('新增桌面失败：' + response.msg);
            }
        }
    };

    // 处理导航提交事件
    const handleNavSubmit = async (navForm: Partial<NavForm>) => {
        if (currentParentNav.value.id) {
            navForm.parentId = currentParentNav.value.id;
            navForm.parentCode = currentParentNav.value.code;
        }

        if (navForm.id && navForm.code) {
            // 走更新
            const res = await navApi.updateNav(navForm.code, navForm);
            if (res.code === 200) {
                feedback.success('更新导航成功');
                navDialogVisible.value = false;
                (desktopNavDrawerRef.value as any).handleSearch();
            } else {
                feedback.error('更新导航失败');
            }
        } else {
            // 走新增
            const res = await navApi.createNav(navForm);
            if (res.code === 200) {
                feedback.success('新增导航成功');
                navDialogVisible.value = false;

                (desktopNavDrawerRef.value as any).handleSearch();

                // 新增页面的情况下，跳转到页面设计器
                if (!navForm.pageCode) {
                    jumpToDesigner('page', res.result.pageCode, {
                        desktopCode: currentDesktop.value.code,
                        navCode: res.result.code,
                        bizGroup: currentDesktop.value.bizGroup,
                    });
                }
            } else {
                feedback.error('新增导航失败');
            }
        }
    };

    // 处理导航分组提交事件
    const handleNavGroupSubmit = async (navGroupForm: Partial<NavGroup>) => {
        if (navGroupForm.code) {
            // 修改
            const response = await navGroupApi.updateNavGroup(navGroupForm.code, navGroupForm);
            if (response.code === 200) {
                feedback.success('修改导航分组成功');
                navGroupDialogVisible.value = false;

                (desktopNavGroupDrawerRef.value as any).handleSearch();
            } else {
                feedback.error('修改导航分组失败：' + response.msg);
            }
        } else {
            // 新增
            const response = await navGroupApi.createNavGroup(navGroupForm);
            if (response.code === 200) {
                feedback.success('新增导航分组成功');
                navGroupDialogVisible.value = false;

                (desktopNavGroupDrawerRef.value as any).handleSearch();
            } else {
                feedback.error('新增导航分组失败：' + response.msg);
            }
        }
    };

    // 处理导航新增
    const handleNavAdd = () => {
        currentNav.value = {};
        currentParentNav.value = {};
        navDialogVisible.value = true;
    };

    // 处理导航编辑
    const handleNavEdit = (data: Nav) => {
        currentNav.value = data;
        navDialogVisible.value = true;
    };

    // 处理导航分组新增
    const handleNavGroupAdd = () => {
        currentNavGroup.value = {};
        navGroupDialogVisible.value = true;
    };

    // 处理导航分组编辑
    const handleNavGroupEdit = (data: NavGroup) => {
        currentNavGroup.value = data;
        navGroupDialogVisible.value = true;
    };

    // 处理导航新增子导航
    const handleNavAddChild = (data: Nav) => {
        currentNav.value = {};
        currentNav.value.parentCode = data.code;
        currentParentNav.value = data;
        navDialogVisible.value = true;
    };

    // 网站变化，桌面查询表单的网站编码变化
    watch(
        () => siteStore.currentSiteCode,
        (newVal) => {
            desktopSearchForm.value.siteCode = newVal;
        }
    );

    // 桌面查询表单变化，查询桌面列表
    watch(
        () => desktopSearchForm.value,
        () => {
            handleDesktopSearch();
        },
        { deep: true, immediate: true }
    );

    onMounted(() => {
        handleDesktopSearch();
    });
</script>
