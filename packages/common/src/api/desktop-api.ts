import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import { Desktop, DesktopSearchForm, Page, PaginationParams } from '@smartdesk/common/types';

export interface DesktopApi {
    /**
     * 分页多条件查询桌面列表
     *
     * @param searchForm 查询表单
     * @param paginationParams 分页参数
     * */
    getDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Desktop>>;

    /**
     * 新增桌面
     *
     * @param data 桌面表单
     * */
    createDesktop(data: Partial<Page>): Promise<RestResultResponse<Page>>;

    /**
     * 修改桌面
     *
     * @param code 桌面编码
     * @param data 桌面表单
     * */
    updateDesktop(code: string, data: Partial<Page>): Promise<RestResultResponse<Page>>;

    /**
     * 根据桌面编码查询桌面
     *
     * @param code 桌面编码
     * */
    getDesktop(code: string): Promise<RestResultResponse<Desktop>>;
}

export const desktopApi: DesktopApi = {
    /**
     * 查询所有桌面列表
     *
     * @param searchForm 查询表单
     * @param paginationParams 分页参数
     * */
    getDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Desktop>> {
        return smartDeskHttpClient.post('/desktop/list', searchForm).params(paginationParams).send();
    },

    /**
     * 新增桌面
     *
     * @param data 桌面表单
     * */
    createDesktop(data: Partial<Page>): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post('/desktop', data).send();
    },

    /**
     * 修改桌面
     *
     * @param code 桌面编码
     * @param data 桌面表单
     * */
    updateDesktop(code: string, data: Partial<Page>): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post(`/desktop/${code}`, data).send();
    },

    /**
     * 根据桌面编码查询桌面
     *
     * @param code 桌面编码
     * */
    getDesktop(code: string): Promise<RestResultResponse<Desktop>> {
        return smartDeskHttpClient.get(`/desktop/${code}`).send();
    },
};
