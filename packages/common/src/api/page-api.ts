import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { Page, PageSearchForm, PaginationParams } from '@smartdesk/common/types';

export interface PageApi {
    /**
     * 获取页面信息
     *
     * @param pageCode 页面编码
     */
    getPageDetails(pageCode: string): Promise<RestResultResponse<Page>>;

    /**
     * 修改页面信息
     *
     * @param code 页面code
     * @param page 页面信息
     */
    updatePage(code: string, page: Page): Promise<RestResultResponse<Page>>;

    /**
     * 修改页面布局信息
     *
     * @param code 页面code
     * @param page 页面信息
     * */
    updatePageLayout(code: string, page: Page): Promise<RestResultResponse<Page>>;

    // 查询所有页面列表
    getPageList(
        searchForm: Partial<PageSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Page>>;

    // 添加页面
    addPage(data: any): Promise<RestResultResponse<any>>;

    // 批量启用页面
    batchEnablePage(data: any): Promise<RestResponse>;

    // 批量禁用页面
    batchDisablePage(data: any): Promise<RestResponse>;

    findPageSection(code: string): Promise<RestResultResponse<Array<any>>>;

    // 更新页面楼层
    updatePageSection(code: string, data: any): Promise<RestResponse>;

    // 更新页面楼层顺序
    updatePageSectionOrder(data: any): Promise<RestResponse>;

    // 复制页面
    copyPage(data: any): Promise<RestResultResponse<Page>>;
}

export const pageApi: PageApi = {
    /**
     * 获取页面管理数据
     *
     * @param pageCode 页面编码
     */
    getPageDetails(pageCode: string): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.get(`/view`).params({ code: pageCode }).send();
    },

    /**
     * 修改页面管理数据
     *
     * @param code 页面code
     * @param page 页面信息
     */
    updatePage(code: string, page: Page): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post(`/page/${code}`, page).send();
    },

    /**
     * 修改页面布局信息
     *
     * @param code 页面code
     * @param page 页面信息
     * */
    updatePageLayout(code: string, page: Page): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post(`/page/${code}/layout`, page).send();
    },

    // 查询页面列表
    getPageList(
        searchForm: Partial<PageSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Page>> {
        return smartDeskHttpClient.post('/page/list', searchForm).params(paginationParams).send();
    },

    // 添加页面
    addPage(data: any): Promise<RestResultResponse<any>> {
        return smartDeskHttpClient.post('/page', data).send();
    },

    findPageSection(pageCode: string): Promise<RestResultResponse<Array<any>>> {
        return smartDeskHttpClient.get(`/page/page-section`, { params: { pageCode } }).send();
    },

    // 更新页面楼层
    updatePageSection(code: string, data: any): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/page/section/${code}`, data).send();
    },

    // 更新页面楼层顺序
    updatePageSectionOrder(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/page/section/order`, data).send();
    },

    // 批量启用页面
    batchEnablePage(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put('/page/enable', data).send();
    },

    // 批量禁用页面
    batchDisablePage(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put('/page/disable', data).send();
    },

    // 复制页面
    copyPage(data: any): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post('/page/copy', data).send();
    },
};
