import { AuditBean } from '@chances/portal_common_core';
import {
    CellItemData,
    ComponentLayoutFile,
    ComponentStyleLayoutFile,
    LayoutFile,
    SectionLayoutFile,
    SectionLayoutLayoutFile,
} from '@smartdesk/common/types';

// 管理状态相关
export interface AdminStatus {
    // 删除状态：0 正常、-1 预删除、-2 已删除
    delFlag: number;

    // 启用禁用状态：0 禁用、1 启用
    status: number;
}

// 发布状态相关
export interface PublishStatus extends AdminStatus {
    // 前台是否可见状态：0 不可见、1 可见
    visibleStatus: number;

    // 上下线状态：0 待上线、1 已上线、2 已下线
    onlineStatus: number;

    // 审核状态：0 待送审、1 审核中、2 审核通过、3 审核驳回
    auditStatus: number;
}

// 网站配置
export interface SiteConfig {
    // 是否可编辑组件样式
    canEditComponentStyle: boolean;
}

// 网站
export interface Site extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 编码
    code: string;

    // 名称
    name: string;

    // 网站配置
    config: Partial<SiteConfig>;

    // 数据域
    domain: string;

    // 组织 ID
    orgId: number;
}

// 网站目录
export interface SiteFolder extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 父ID
    parentId: number;

    // 父编码
    parentCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 目录
    location: string;

    // 数据域
    domain: string;

    // 组织 ID
    orgId: number;
}

// 桌面
export type Desktop = Page;

// 页面
export interface Page extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 网站目录 ID
    siteFolderId: number;

    // 网站目录编码
    siteFolderCode: string;

    // 布局 ID
    layoutId: number;

    // 布局编码
    layoutCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    componentType: string;

    // 页面类型
    type: string;

    // 分辨率
    resolution: string;

    // 业务分组
    bizGroup: string;

    // 标签
    tags: string;

    // 缩略图
    icon: string;

    // 样式 json
    layout: LayoutFile;

    // 样式版本
    layoutVersion: number;

    // 所属组织 ID
    orgId: number;

    // 页面下的页面楼层列表
    pageSectionList: PageSection[];
}

// 导航
export interface Nav extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 父ID
    parentId: number;

    // 父编码
    parentCode: string;

    // 编码
    code: string;

    // 标题
    title: string;

    // 所属页面ID
    ownerPageId: number;

    // 所属页面编码
    ownerPageCode: string;

    // 页面编码
    pageCode: string;

    // 导航类型
    type: number;

    // 菜单类型
    menuType: number;

    // 是否默认页面
    defaultPageFlag: boolean;

    // 顺序
    orderNo: number;

    // 推荐策略编码
    ruleCode: string;

    // 规则条件表达式
    ruleExpression: string;

    // 是否反选
    inverted: boolean;

    // 图片配置
    icons: Record<string, any>;

    // 所属组织
    orgId: number;

    // 子导航列表
    children: Nav[];
}

// 导航分组
export interface NavGroup extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 桌面ID
    desktopId: number;

    // 桌面编码
    desktopCode: string;

    // 编码
    code: string;

    // 标题
    title: string;

    // 伸缩效果
    expandEffect: string;

    // 图片配置
    icons: Record<string, any>;

    // 所属组织
    orgId: number;

    // 导航编码列表
    navCodes: string[];
}

// 页面楼层
export interface PageSection extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 页面ID
    pageId: number;

    // 页面编码
    pageCode: string;

    // 楼层定义ID
    sectionId: number;

    // 楼层定义编码
    sectionCode: string;

    // 父ID
    parentId: number;

    // 父编码
    parentCode: string;

    // 引用楼层ID
    refSectionId: number;

    // 引用楼层编码
    refSectionCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    componentType: string;

    // 顺序
    orderNo: number;

    // 图片
    icon: string;

    // 推荐策略编码
    ruleCode: string;

    // 规则条件表达式
    ruleExpression: string;

    // 是否反选
    inverted: boolean;

    // 数据源类型
    dsType: string;

    // 数据源编码
    dsCode: string;

    // 数据源名称
    dsName: string;

    // 数据源参数
    dsParams: Record<string, any>;

    // 生效时间
    validTime: string;

    // 失效时间
    expireTime: string;

    // 调度策略编码
    scheduleRuleCode: string;

    // 调度规则条件表达式
    scheduleRuleExpression: string;

    // 调度规则
    scheduleRule: ScheduleRule;

    // 样式 json
    layout: LayoutFile;

    // 样式版本
    layoutVersion: number;

    // 所属组织 ID
    orgId: number;

    // 楼层坑位列表
    pageCellList: PageCell[];
}

// 坑位
export interface PageCell extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 页面ID
    pageId: number;

    // 页面编码
    pageCode: string;

    // 页面楼层ID
    pageSectionId: number;

    // 页面楼层编码
    pageSectionCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    componentType: string;

    // 组件样式编码
    componentStyleCode: string;

    // 样式 json
    layout: LayoutFile;

    // 样式版本
    layoutVersion: number;

    // 数据源类型
    dsType: string;

    // 数据源编码
    dsCode: string;

    // 数据源名称
    dsName: string;

    // 数据源参数
    dsParams: Record<string, any>;

    // 所属组织 ID
    orgId: number;

    // 坑位元素列表
    pageCellItemList: PageCellItem[];
}

// 坑位元素
export interface PageCellItem extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 坑位ID
    cellId: number;

    // 坑位编码
    cellCode: string;

    // 分类
    type: number;

    // 是否默认
    defaultFlag: number;

    // 生效时间
    validTime: string;

    // 失效时间
    expireTime: string;

    // 编码
    code: string;

    // 标题
    title: string;

    // 副标题
    subTitle: string;

    // 图片配置
    icons: Record<string, any>;

    // 基础角标
    baseCorner: string;

    // 运营角标
    opCorner: string;

    // 跳转页面类型
    linkType: string;

    // 跳转页面类型扩展参数
    extraParams: Record<string, any>;

    // 数据类型
    dataType: string;

    // 数据编码
    dataCode: string;

    // 数据编码
    dataName: string;

    // 顺序
    orderNo: number;

    // 推荐策略编码
    ruleCode: string;

    // 规则条件表达式
    ruleExpression: string;

    // 是否反选
    inverted: boolean;

    // 调度策略编码
    scheduleRuleCode: string;

    // 调度规则条件表达式
    scheduleRuleExpression: string;

    // 调度规则
    scheduleRule: ScheduleRule;

    // 所属组织
    orgId: number;

    // 坑位元素数据模型
    itemData: CellItemData;
}

// 调度规则
export interface ScheduleRule extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 编码
    code: string;

    // 规则条件
    scheduleConfig: ScheduleConfig;

    // 规则条件表达式
    scheduleRuleExpression: string;
}

// 调度配置
export interface ScheduleConfig {
    // 调度类型
    scheduleType: ScheduleType;

    // 天列表
    dayList: number[];

    // 开始时间
    startTime: string;

    // 结束时间
    endTime: string;
}

// 调度类型：
// 0-每天
// 1-每周
// 2-每月
export type ScheduleType = 0 | 1 | 2;

// 组件
export interface Component extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    type: string;

    // 组件分类
    category: string;

    // 目录
    catalog: string;

    // 样式 json
    layout: ComponentLayoutFile;

    // 版本
    version: number;

    // 标签
    tags: string;

    // 缩略图
    icon: string;

    // 作者
    author: string;

    // 描述
    description: string;

    // 可编辑范围
    scopes: string[];

    // 所属组织 ID
    orgId: number;

    // 组件样式列表
    styles: ComponentStyle[];
}

// 组件样式
export interface ComponentStyle extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 组件ID
    componentId: number;

    // 组件编码
    componentCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    type: string;

    // 组件分类
    category: string;

    // 样式 json
    layout: ComponentStyleLayoutFile;

    // 版本
    layoutVersion: number;

    // 缩略图
    icon: string;

    // 所属组织 ID
    orgId: number;
}

// 布局
export interface Layout extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 类型
    type: string;

    // 分辨率
    resolution: string;

    // 样式 json
    layout: SectionLayoutLayoutFile;

    // 样式版本
    layoutVersion: number;

    // 缩略图
    icon: string;

    // 所属组织 ID
    orgId: number;
}

// 楼层定义
export interface Section extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 布局 ID
    layoutId: number;

    // 布局编码
    layoutCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 类型
    type: string;

    // 顺序
    orderNo: number;

    // 分辨率
    resolution: string;

    // 标签
    tags: string;

    // 缩略图
    icon: string;

    // 样式 json
    layout: SectionLayoutFile;

    // 样式版本
    layoutVersion: number;

    // 所属组织 ID
    orgId: number;
}

// 跳转路由
export interface LinkType extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 分类
    genre: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 顺序
    orderNo: number;

    // 数据类型
    dataType: string;

    // 使用说明
    summary: string;

    // 业务分组
    bizGroups: string;

    // 是否需要参数
    hasParam: boolean;

    // 参数实例
    paramDesc: string;

    // 扩展参数定义 JSON
    extraParams: any[];

    // 所属组织 ID
    orgId: number;
}

// 推荐策略
export interface PersonalRule extends AuditBean, AdminStatus {
    // ID
    id: number;

    // 网站ID
    siteId: number;

    // 网站编码
    siteCode: string;

    // 推荐策略目录ID
    folderId: number;

    // 推荐策略目录编码
    folderCode: string;

    // 编码
    code: string;

    // 名称
    name: string;

    // 作用域
    scope: number;

    // 规则条件
    rules: Record<string, any>;

    // 规则条件表达式
    ruleExpression: string;

    // 所属组织 ID
    orgId: number;
}

// 推荐策略目录
export interface PersonalRuleFolderModel extends AuditBean, AdminStatus {
    // 推荐策略目录名称
    name: string;

    // 推荐策略目录编码
    code: string;

    // 推荐策略目录父编码
    parentCode: string;

    // 所属组织 ID
    orgId: number;

    // 推荐策略子目录
    children: PersonalRuleFolderModel[];
}

// 维度
export interface Dimension {
    // id
    id: number;

    // 父 id
    parentId: number;

    // 编码
    code: string;

    // 名称
    name: string;

    // 类型
    type: string;

    // 首字母
    initials: string;

    // 启用禁用状态
    enableStatus: string;

    // 描述
    description: string;

    // 是否有权限
    hasPermission: boolean;

    // 子列表
    children: Dimension[];
}

// 用户分组
export interface UserGroup {
    id: number;
    platform: string;
    name: string;
    code: string;
    type: number;
    status: number;
    desc: string;
    createTime: string;
    updateTime: string;
    validTime: string;
    expireTime: string;
    checkValid: boolean;
}

// 机顶盒
export interface Box extends AuditBean, PublishStatus {
    // ID
    id: number;

    // 编码
    code: string;

    // 名称
    name: string;

    // 机顶盒厂商
    boxGroup: string;

    // 能力
    capability: string[];
}
