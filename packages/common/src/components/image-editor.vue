<template>
    <el-upload
        v-if="!imageUrl"
        list-type="picture-card"
        :show-file-list="false"
        :http-request="handleUpload"
        :on-success="handleUploadSuccess"
        :disabled="disabled">
        <el-icon>
            <Plus />
        </el-icon>
    </el-upload>

    <div v-if="imageUrl" class="relative group">
        <el-image class="w-[200px] border border-gray-200 rounded-lg" :src="imageUrl">
            <template #error>
                <image-error-fallback text="图片损坏" />
            </template>
        </el-image>

        <div
            class="absolute inset-0 bg-white opacity-0 group-hover:opacity-40 transition-opacity duration-300 rounded-lg"></div>

        <div
            class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
            <div
                @click.stop="handlePreview"
                class="w-10 h-10 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center cursor-pointer transition-colors shadow-lg">
                <el-icon color="white" size="20">
                    <View />
                </el-icon>
            </div>

            <div v-if="!disabled" class="relative">
                <el-upload
                    :show-file-list="false"
                    :http-request="handleUpload"
                    :on-success="handleUploadSuccess"
                    class="inline-block">
                    <div
                        class="w-10 h-10 bg-yellow-500 hover:bg-yellow-600 rounded-full flex items-center justify-center cursor-pointer transition-colors shadow-lg">
                        <el-icon color="white" size="20">
                            <Edit />
                        </el-icon>
                    </div>
                </el-upload>
            </div>

            <div
                v-if="!disabled"
                @click.stop="handleRemove"
                class="w-10 h-10 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center cursor-pointer transition-colors shadow-lg">
                <el-icon color="white" size="20">
                    <Delete />
                </el-icon>
            </div>
        </div>
    </div>

    <el-dialog v-model="dialogVisible" width="50%" title="图片预览">
        <el-image class="w-full" :src="dialogImageUrl">
            <template #error>
                <image-error-fallback text="图片损坏" />
            </template>
        </el-image>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { computed, PropType, ref } from 'vue';
    import { Delete, Edit, Plus, View } from '@element-plus/icons-vue';
    import type { UploadRequestOptions } from 'element-plus';
    import { RestResultResponse } from '@chances/portal_common_core';
    import { storageApi } from '@smartdesk/common/api';
    import ImageErrorFallback from './image-error-fallback.vue';

    // 参数
    const props = defineProps({
        modelValue: {
            type: [String, null] as PropType<string | null>,
            default: null,
            required: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: string): void;
        (e: 'change', value: string): void;
    }>();

    // 预览图地址
    const dialogImageUrl = ref<string>('');

    // 预览图显隐
    const dialogVisible = ref<boolean>(false);

    // imageUrl 定义为可写的计算属性，以响应式地处理 v-model
    const imageUrl = computed({
        // get() 会在读取 imageUrl 时调用，它总是返回 prop 的最新值
        get() {
            return props.modelValue ?? '';
        },
        // set() 会在给 imageUrl 赋值时调用
        set(newValue: string) {
            // 通过 emit 更新父组件的 v-model
            emit('update:modelValue', newValue);
            // 触发 change 事件
            emit('change', newValue);
        },
    });

    // 移除图片
    const handleRemove = () => {
        // 直接给 imageUrl 赋值，会自动触发 computed 的 set 方法
        imageUrl.value = '';
    };

    // 预览图片
    const handlePreview = () => {
        dialogImageUrl.value = imageUrl.value;
        dialogVisible.value = true;
    };

    // 处理上传图片
    const handleUpload = (options: UploadRequestOptions) => {
        return storageApi.uploadFile(options.file);
    };

    // 上传成功
    const handleUploadSuccess = (response: RestResultResponse<string>) => {
        // 直接给 imageUrl 赋值，会自动触发 computed 的 set 方法
        imageUrl.value = response.result;
    };
</script>
