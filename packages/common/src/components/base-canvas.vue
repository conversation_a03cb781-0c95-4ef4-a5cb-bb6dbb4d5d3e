<template>
    <div class="relative w-full" :style="{ height: canvasHeight }">
        <div ref="horizontalRef" class="horizontal absolute w-full h-[30px]" />
        <div ref="verticalRef" class="vertical absolute w-[30px] h-full" />

        <div>
            <div
                v-if="showMoveButtons"
                @click="moveContent('left')"
                class="absolute left-[40px] top-1/2 -translate-y-1/2 w-8 h-12 bg-white/60 hover:bg-white/80 rounded-r flex items-center justify-center cursor-pointer transition-colors z-[22]">
                <el-icon class="text-gray-600">
                    <ArrowLeft />
                </el-icon>
            </div>
            <div
                v-if="showMoveButtons"
                @click="moveContent('right')"
                class="absolute right-[10px] top-1/2 -translate-y-1/2 w-8 h-12 bg-white/60 hover:bg-white/80 rounded-l flex items-center justify-center cursor-pointer transition-colors z-[22]">
                <el-icon class="text-gray-600">
                    <ArrowRight />
                </el-icon>
            </div>
        </div>

        <slot name="navigation" />

        <div
            id="base-canvas-container"
            ref="containerRef"
            class="container absolute top-[30px] left-[30px] right-0 bottom-0 overflow-auto bg-white box-border z-10"
            :style="containerStyle">
            <div
                ref="contentRef"
                id="canvas-content"
                class="origin-top-left w-full transition-transform duration-150 ease-out"
                :style="contentStyle">
                <slot name="overlay" />
            </div>
        </div>

        <div class="absolute bottom-5 right-5 flex items-center gap-2 bg-white p-1.5 rounded shadow-md z-[22]">
            <el-button
                @click="resetView"
                icon="Refresh"
                size="small"
                title="重置视图"
                class="flex items-center justify-center px-2 py-1 text-xs border border-gray-200 bg-white rounded hover:bg-gray-50" />
            <div class="h-4 w-[1px] bg-gray-200"></div>
            <el-button
                @mousedown="
                    (e: any) => {
                        if (!e.currentTarget.disabled) zoomLongPress('out');
                    }
                "
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Minus"
                size="small"
                :disabled="scale <= MIN_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" />
            <span class="min-w-[48px] text-center text-sm">{{ Math.round(scale * 100) }}%</span>
            <el-button
                @mousedown="
                    (e: any) => {
                        if (!e.currentTarget.disabled) zoomLongPress('in');
                    }
                "
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Plus"
                size="small"
                :disabled="scale >= MAX_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, CSSProperties, inject, nextTick, onBeforeUnmount, onMounted, provide, ref, watch } from 'vue';
    import Guides from '@scena/guides';
    import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

    // 基础画布
    defineOptions({ name: 'BaseCanvas' });

    // 组件属性定义
    const props = withDefaults(
        defineProps<{
            maxRulerSize?: number;
            minScale?: number;
            maxScale?: number;
            zoomStep?: number;
            contentWidth?: number;
            contentHeight?: number;
            canvasHeight?: string;
            moveStep?: number;
        }>(),
        {
            maxRulerSize: 999999,
            minScale: 0.1,
            maxScale: 2.4,
            zoomStep: 0.01,
            contentWidth: undefined,
            contentHeight: undefined,
            canvasHeight: '100%',
            moveStep: 100,
        }
    );

    // 模板引用
    const containerRef = ref<HTMLElement>();
    const horizontalRef = ref<HTMLElement>();
    const verticalRef = ref<HTMLElement>();
    const contentRef = ref<HTMLElement>();

    // 响应式状态
    const scale = ref<number>(1);
    const rulersReady = ref(false);
    const contentPosition = ref({ x: 0, y: 0 });

    // 依赖注入和计算属性
    const centerWidth = inject('centerWidth', 1200);
    const getCenterWidth = computed(() => (centerWidth as any).value ?? centerWidth);
    const getContentWidth = computed(() => props.contentWidth);
    const MIN_SCALE = computed(() => props.minScale);
    const MAX_SCALE = computed(() => props.maxScale);
    const ZOOM_STEP = computed(() => props.zoomStep);

    // 计算是否需要显示移动按钮
    const showMoveButtons = computed(() => {
        if (!containerRef.value || !props.contentWidth) return false;

        // 获取容器宽度（不包括标尺）
        const containerWidth = containerRef.value.clientWidth;

        // 计算缩放后的内容宽度
        const scaledContentWidth = props.contentWidth * scale.value;

        // 当缩放后的内容宽度大于容器宽度时，显示移动按钮
        return scaledContentWidth > containerWidth;
    });

    // 计算水平移动的边界
    const calculateXBounds = () => {
        if (!containerRef.value || !props.contentWidth) {
            return { minX: 0, maxX: 0 };
        }

        const containerWidth = containerRef.value.clientWidth;
        const scaledContentWidth = props.contentWidth * scale.value;

        // 当内容宽度小于等于容器宽度时，不允许移动
        if (scaledContentWidth <= containerWidth) {
            return { minX: 0, maxX: 0 };
        }

        // 计算移动边界
        const maxX = 0; // 最右边界（内容左边缘对齐容器左边缘）
        const minX = -(scaledContentWidth - containerWidth) / scale.value; // 最左边界

        return { minX, maxX };
    };

    // 应用水平边界限制
    const applyXBounds = (x: number) => {
        const { minX, maxX } = calculateXBounds();
        return Math.max(minX, Math.min(maxX, x));
    };

    // 提供缩放给子组件
    provide('epg_scale', scale);

    // 类型定义
    type ZoomDirection = 'in' | 'out';

    // 标尺实例
    const rulers = {
        horizontal: ref<Guides | null>(null),
        vertical: ref<Guides | null>(null),
    };

    // 根据缩放比计算合适的刻度间隔
    const calculateRulerSettings = (scale: number) => {
        let unit = 50;
        let longLineSize = 10;

        if (scale <= 0.1) {
            unit = 500;
            longLineSize = 4;
        } else if (scale <= 0.2) {
            unit = 200;
            longLineSize = 5;
        } else if (scale <= 0.3) {
            unit = 100;
            longLineSize = 5;
        } else if (scale <= 0.5) {
            unit = 50;
            longLineSize = 4;
        }

        return { unit, longLineSize };
    };

    // 更新标尺配置
    const getDynamicRulerConfig = () => {
        const settings = calculateRulerSettings(scale.value);

        return {
            displayDragPos: true,
            useResizeObserver: true,
            backgroundColor: '#f5f5f5',
            lineColor: '#ccc',
            textColor: '#666',
            range: [0, props.maxRulerSize] as [number, number],
            zoom: scale.value,
            unit: settings.unit,
            longLineSize: settings.longLineSize,
            shortLineSize: 1,
        };
    };

    // 画布容器样式
    const containerStyle = computed<CSSProperties>(() => ({
        backgroundImage: `radial-gradient(#d0d3d9 ${1 * scale.value}px, transparent ${1 * scale.value}px)`,
        backgroundSize: `${20 * scale.value}px ${20 * scale.value}px`,
        cursor: 'grab',
    }));

    // 内容元素样式
    const contentStyle = computed<CSSProperties>(() => ({
        width: `${props.contentWidth}px`,
        height: `${props.contentHeight}px`,
    }));

    // 缩放相关状态
    let zoomTimer: ReturnType<typeof setTimeout> | null = null;
    let zoomInterval = 300;

    // 直接更新 DOM 样式的核心函数
    const updateDOMTransform = (x: number, y: number) => {
        const contentElement = document.getElementById('canvas-content');
        if (contentElement) {
            contentElement.style.transform = `scale(${scale.value}) translate(${x}px, ${y}px)`;
            // 强制浏览器重新计算样式
            contentElement.offsetHeight;
        }
    };

    // 同步标尺位置
    const syncRulers = (x: number, y: number) => {
        if (rulersReady.value) {
            nextTick(() => {
                rulers.horizontal.value?.scroll(-x);
                rulers.vertical.value?.scroll(-y);
            });
        }
    };

    // 初始化标尺
    const initRulers = (): void => {
        const checkAndInit = () => {
            if (horizontalRef.value && verticalRef.value) {
                const hRect = horizontalRef.value.getBoundingClientRect();
                const vRect = verticalRef.value.getBoundingClientRect();

                if (hRect.width > 0 && hRect.height > 0 && vRect.width > 0 && vRect.height > 0) {
                    rulers.horizontal.value = new Guides(horizontalRef.value, {
                        ...getDynamicRulerConfig(),
                        type: 'horizontal',
                        rulerStyle: {
                            left: '30px',
                            width: 'calc(100% - 30px)',
                            height: '100%',
                        },
                    });

                    rulers.vertical.value = new Guides(verticalRef.value, {
                        ...getDynamicRulerConfig(),
                        type: 'vertical',
                        rulerStyle: {
                            top: '30px',
                            height: 'calc(100% - 30px)',
                            width: '100%',
                        },
                    });

                    rulersReady.value = true;
                    return true;
                }
            }
            return false;
        };

        if (!checkAndInit()) {
            const tryInit = () => {
                if (!checkAndInit()) {
                    requestAnimationFrame(tryInit);
                }
            };
            requestAnimationFrame(tryInit);
        }
    };

    // 更新标尺缩放
    const updateRulerZoom = (): void => {
        if (rulersReady.value) {
            rulers.horizontal.value?.setState({ zoom: scale.value });
            rulers.vertical.value?.setState({ zoom: scale.value });
        }
    };

    // 缩放相关函数
    const zoom = (direction: ZoomDirection, amount: number = ZOOM_STEP.value): void => {
        const delta = direction === 'in' ? amount : -amount;
        const newScale = Math.min(Math.max(scale.value + delta, MIN_SCALE.value), MAX_SCALE.value);
        scale.value = Number(newScale.toFixed(2));
    };

    const zoomLongPress = (direction: ZoomDirection) => {
        if (
            (direction === 'in' && scale.value >= MAX_SCALE.value) ||
            (direction === 'out' && scale.value <= MIN_SCALE.value)
        )
            return;
        zoom(direction);
        zoomInterval = Math.max(50, zoomInterval - 30);
        zoomTimer = setTimeout(() => zoomLongPress(direction), zoomInterval);
    };

    const clearZoomLongPress = () => {
        if (zoomTimer) clearTimeout(zoomTimer);
        zoomTimer = null;
        zoomInterval = 300;
    };

    // 边界计算函数
    const getActualContentHeight = () => {
        if (!contentRef.value) return 0;
        return contentRef.value.getBoundingClientRect().height;
    };

    const calculateYBounds = () => {
        if (!containerRef.value) return { minY: 0, maxY: 0 };

        const contentHeight = getActualContentHeight() / scale.value;
        if (contentHeight === 0) return { minY: 0, maxY: Infinity };

        const containerHeight = containerRef.value.clientHeight / scale.value;
        const minY = 0;
        const maxY = Math.max(0, contentHeight - containerHeight + 100);
        return { minY, maxY };
    };

    // 应用边界限制
    const applyBounds = (y: number) => {
        const contentHeight = getActualContentHeight();
        if (contentHeight === 0) return y;

        const { minY, maxY } = calculateYBounds();
        let limitedY = Math.min(y, -minY);
        if (maxY !== Infinity) {
            limitedY = Math.max(limitedY, -maxY);
        }
        return limitedY;
    };

    // 更新内容位置并同步标尺（主要的位置更新函数）
    const updatePositionAndRulers = (x: number, y: number) => {
        const limitedX = applyXBounds(x);
        const limitedY = applyBounds(y);

        // 如果位置没有变化，直接返回
        if (contentPosition.value.x === limitedX && contentPosition.value.y === limitedY) {
            return;
        }

        // 更新响应式数据
        contentPosition.value = { x: limitedX, y: limitedY };

        // 直接更新 DOM 样式
        updateDOMTransform(limitedX, limitedY);

        // 同步标尺位置
        syncRulers(limitedX, limitedY);
    };

    // 移动内容
    const moveContent = (direction: 'left' | 'right') => {
        const currentX = contentPosition.value.x;
        const step = props.moveStep;

        let newX: number;
        if (direction === 'left') {
            // 向左移动（内容向右移动，x值增加）
            newX = currentX + step;
        } else {
            // 向右移动（内容向左移动，x值减少）
            newX = currentX - step;
        }

        // 应用边界限制并更新位置
        updatePositionAndRulers(newX, contentPosition.value.y);
    };

    // 滚轮事件处理
    const handleWheel = (e: WheelEvent): void => {
        e.preventDefault();
        const newY = contentPosition.value.y - e.deltaY;
        const limitedY = applyBounds(newY);

        if (contentPosition.value.y === limitedY) return;

        contentPosition.value = { x: contentPosition.value.x, y: limitedY };
        updateDOMTransform(contentPosition.value.x, limitedY);
        syncRulers(contentPosition.value.x, limitedY);
    };

    // 缩放计算和处理
    const calcScale = (): number => {
        const cw = getCenterWidth.value;
        const contentW = getContentWidth.value;
        if (!contentW) return 1;
        return Math.min(1, cw / contentW);
    };

    const handleScaleChange = (): void => {
        const newScale = calcScale();
        if (newScale !== scale.value) {
            scale.value = newScale;
        }
    };

    // 重置视图
    const resetView = () => {
        handleScaleChange();
        updatePositionAndRulers(0, 0);
    };

    // 事件绑定和解绑
    const bindEventListener = (): void => {
        window.addEventListener('resize', handleScaleChange);
        if (containerRef.value) {
            containerRef.value.addEventListener('wheel', handleWheel, {
                passive: false,
            });
        }
    };

    const unBindEventListener = (): void => {
        window.removeEventListener('resize', handleScaleChange);
        if (containerRef.value) {
            containerRef.value.removeEventListener('wheel', handleWheel);
        }
    };

    // 响应式监听器
    watch([getCenterWidth, getContentWidth], handleScaleChange);

    watch(scale, () => {
        updateRulerZoom();
        // 缩放变化时重新应用边界限制并更新 DOM
        const limitedX = applyXBounds(contentPosition.value.x);
        const limitedY = applyBounds(contentPosition.value.y);
        if (limitedX !== contentPosition.value.x || limitedY !== contentPosition.value.y) {
            updatePositionAndRulers(limitedX, limitedY);
        } else {
            // 即使位置没变，缩放变化时也需要更新 transform
            updateDOMTransform(contentPosition.value.x, contentPosition.value.y);
        }
    });

    watch(rulersReady, (ready) => {
        if (ready && (contentPosition.value.x !== 0 || contentPosition.value.y !== 0)) {
            syncRulers(contentPosition.value.x, contentPosition.value.y);
        }
    });

    watch(
        () => getActualContentHeight(),
        (newHeight, oldHeight) => {
            if (newHeight > 0 && oldHeight === 0 && contentPosition.value.y !== 0) {
                nextTick(() => {
                    updatePositionAndRulers(contentPosition.value.x, contentPosition.value.y);
                });
            }
        }
    );

    // 生命周期钩子
    onMounted(() => {
        nextTick(() => {
            initRulers();
            bindEventListener();
            handleScaleChange();
            updateDOMTransform(contentPosition.value.x, contentPosition.value.y);
        });
    });

    onBeforeUnmount(() => {
        unBindEventListener();
    });

    // 导出方法
    defineExpose({
        updatePositionAndRulers,
    });
</script>

<style scoped>
    :deep(.scena-guides-guide) {
        display: none !important;
    }
</style>
