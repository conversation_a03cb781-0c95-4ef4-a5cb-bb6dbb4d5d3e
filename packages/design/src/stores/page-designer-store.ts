import { defineStore, StateTree } from 'pinia';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import {
    CellItemData,
    ComponentStyle,
    ContentData,
    DesignMode,
    Desktop,
    LayoutFile,
    Page,
    PageCell,
    PageCellItem,
    PageSection,
    ScheduleRule,
    Section,
} from '@smartdesk/common/types';
import { computed, nextTick, reactive, ref } from 'vue';
import { cellApi, cmsApi, desktopApi, pageApi, pageSectionApi, storageApi } from '@smartdesk/common/api';
import { useCanvasDownload, useFeedback } from '@smartdesk/common/composables';
import { CELL_ITEM_TYPE } from '@smartdesk/common/constant';
import { useCornerMarkStore } from '@smartdesk/common/stores';
import { DEFAULT_ITEM_TEMPLATE, EMPTY_SCHEDULE_RULE } from '@smartdesk/design/constant';
import { convertDsInfoMapToObject, convertStringArrayMapToObject } from '@smartdesk/design/utils';
import { useVirtualList } from '@smartdesk/design/composables';

// 元素类型：桌面、页面、页面楼层、坑位
export type ElementType = 'Desktop' | 'Page' | 'PageSection' | 'Cell';

// 页面设计器状态存储
export const usePageDesignerStore = defineStore(
    'page-designer-store',
    () => {
        // 组合函数
        const feedback = useFeedback();
        const cornerMarkStore = useCornerMarkStore();
        const { visibleSectionCodes, processedSections, startSectionObserver, stopSectionObserver } = useVirtualList(
            'data-section-code',
            (newVisibleSections) => {
                // 构建新楼层的元素映射
                buildElementMap(newVisibleSections);

                // 只处理新进入视口且未处理过的楼层
                batchConvertToCellItemData(newVisibleSections);
            }
        );

        // 模式：页面设计器、桌面设计器；默认为页面设计器
        const mode = ref<'page' | 'desktop'>('page');

        // 业务分组
        const bizGroup = ref<string>('');

        // 桌面编码
        const desktopCode = ref<string>('');

        // 桌面实体
        const desktop = ref<Desktop>({} as Desktop);

        // 当前导航编码
        const navCode = ref<string>('');

        // 当前页面实体
        const page = ref<Page>({} as Page);

        // 元素 Map
        const elementMap = reactive<Map<string, any>>(new Map());

        // key：内容编码，value：内容数据
        const contentMap = reactive<Map<string, ContentData>>(new Map());

        // key：数据源编码，value：内容数据数组
        const contentListMap = reactive<Map<string, ContentData[]>>(new Map());

        // 坑位到坑位数据的映射，key：坑位编码，value：内容数据数组
        const cellContentMap = reactive<Map<string, CellItemData[]>>(new Map());

        // 坑位到选中坑位元素的映射
        const selectedCellItemMap = reactive<Map<string, string>>(new Map());

        // 选中的元素编码
        const selectedElementCode = ref<string>();

        // 选中元素的类型，默认选中页面类型
        const selectedElementType = ref<ElementType>();

        // 是否展示预删除的页面楼层
        const showDeletedPageSections = ref<boolean>(false);

        // 左侧面板激活的 Tab
        const leftPanelActiveTab = ref<string>('section');

        // 右侧面板激活的 Tab
        const rightPanelActiveTab = ref<string>();

        // 选中的元素
        const selectedElement = computed({
            get: () => {
                if (selectedElementCode.value) {
                    return elementMap.get(selectedElementCode.value);
                }
            },
            set: (value) => {
                if (selectedElementCode.value) {
                    elementMap.set(selectedElementCode.value, value);
                }
            },
        });

        // 选中的页面楼层
        const selectedPageSection = computed<PageSection>({
            get: () => {
                if (selectedElementType.value === 'PageSection') {
                    return selectedElement.value;
                }
            },
            set: (value) => {
                if (selectedElementType.value === 'PageSection') {
                    selectedElement.value = value;
                }
            },
        });

        // 选中的楼层坑位
        const selectedPageCell = computed<PageCell>({
            get: () => {
                if (selectedElementType.value === 'Cell') {
                    return selectedElement.value;
                }
            },
            set: (value) => {
                if (selectedElementType.value === 'Cell') {
                    selectedElement.value = value;
                }
            },
        });

        // 获取当前元素类型
        const getElementTypeName = () => {
            if (!selectedElementType.value) {
                return '';
            }

            switch (selectedElementType.value) {
                case 'Desktop':
                    return '桌面';
                case 'Page':
                    return '页面';
                case 'PageSection':
                    return '页面楼层';
                case 'Cell':
                    return '坑位';
                default:
                    return '';
            }
        };

        // 获取页面实体，设置元素 Map
        const getPageDetails = async (code: string) => {
            const res = await pageApi.getPageDetails(code);
            if (res.code === 200) {
                page.value = res.result;
            }
        };

        // 获取桌面实体
        const getDesktop = async () => {
            if (desktopCode.value) {
                const res = await desktopApi.getDesktop(desktopCode.value);
                if (res.code === 200) {
                    desktop.value = res.result;
                }
            }
        };

        // 获取页面楼层
        const getPageSection = async (pageSectionCode: string) => {
            const res = await pageSectionApi.getPageSectionByCode(pageSectionCode);
            if (res.code === 200) {
                elementMap.set(pageSectionCode, res.result);
            }
            return res;
        };

        // 更新页面楼层
        const updatePageSection = async (pageSection: PageSection) => {
            // 更新页面楼层
            const res = await pageSectionApi.updatePageSectionData(pageSection.code, pageSection);
            if (res.code === 200) {
                feedback.success('页面楼层更新成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('页面楼层更新失败：' + res.msg);
            }
            return res;
        };

        // 更新坑位
        const updatePageCell = async (pageCell: PageCell) => {
            const res = await cellApi.updatePageCellData(pageCell.code, pageCell);
            if (res.code === 200) {
                feedback.success('坑位更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位更新失败：' + res.msg);
            }
            return res;
        };

        // 构建元素 Map
        const buildElementMap = (sectionCodes?: string[]) => {
            // 使用 nextTick 将所有更新合并到一个批次中
            const updateElements = () => {
                // 如果没有指定楼层，则只清空并设置页面
                if (!sectionCodes) {
                    elementMap.clear();
                    elementMap.set(page.value.code, page.value);
                    return;
                }

                if (!page.value.pageSectionList) return;

                const sectionsToProcess = sectionCodes
                    ? page.value.pageSectionList.filter((section) => sectionCodes.includes(section.code))
                    : page.value.pageSectionList;

                const cellsToProcess: PageCell[] = [];

                sectionsToProcess.forEach((pageSection) => {
                    ensureDefaultScheduleRule(pageSection);
                    elementMap.set(pageSection.code, pageSection);

                    if (pageSection.pageCellList) {
                        pageSection.pageCellList.forEach((pageCell) => {
                            sortCellItems(pageCell);
                            elementMap.set(pageCell.code, pageCell);
                            cellsToProcess.push(pageCell);
                        });
                    }
                });

                // 处理默认坑位元素
                if (cellsToProcess.length > 0) {
                    nextTick(() => {
                        batchEnsureDefaultCellItemAsync(cellsToProcess);
                    });
                }
            };

            // 将更新包装在 nextTick 中，确保批量执行
            nextTick(updateElements);
        };

        // 获取内容数据
        const getContent = async () => {
            // key：内容类型，value：内容编码数组
            const contentFormMap = new Map<string, string[]>();
            page.value.pageSectionList?.forEach((pageSection) => {
                // 构建 contentFormMap
                pageSection.pageCellList?.forEach((pageCell) => {
                    pageCell.pageCellItemList?.forEach((pageCellItem) => {
                        if (pageCellItem.dataType && pageCellItem.dataCode) {
                            if (contentFormMap.has(pageCellItem.dataType)) {
                                // 合并并去重
                                const existingCodes = contentFormMap.get(pageCellItem.dataType)!;
                                const merged = Array.from(new Set([...existingCodes, ...[pageCellItem.dataCode]]));
                                contentFormMap.set(pageCellItem.dataType, merged);
                            } else {
                                // 新建项
                                contentFormMap.set(pageCellItem.dataType, [...[pageCellItem.dataCode]]);
                            }
                        }
                    });
                });
            });

            const res = await cmsApi.searchContent(convertStringArrayMapToObject(contentFormMap));
            if (res.code === 200) {
                // 清空旧数据
                contentMap.clear();
                for (const [key, value] of Object.entries(res.result)) {
                    contentMap.set(key, value);
                }
            }
        };

        // 获取楼层内容数据
        const getSectionContent = async () => {
            // key：数据源类型，value：数据源编码、数量 的数组
            const contentSectionFormMap = new Map<string, Array<{ dsCode: string; size: number }>>();
            page.value.pageSectionList?.forEach((pageSection) => {
                // 构建 contentSectionFormMap
                if (pageSection.dsCode && pageSection.dsType) {
                    const size =
                        pageSection.pageCellList?.reduce((acc, pageCell) => acc + (pageCell.dsParams?.size ?? 1), 0) ??
                        1;
                    const entry = {
                        dsCode: pageSection.dsCode,
                        size,
                    };
                    if (contentSectionFormMap.has(pageSection.dsType)) {
                        contentSectionFormMap.get(pageSection.dsType)!.push(entry);
                    } else {
                        contentSectionFormMap.set(pageSection.dsType, [entry]);
                    }
                }
            });

            const res = await cmsApi.searchSectionContent(convertDsInfoMapToObject(contentSectionFormMap));
            if (res.code === 200) {
                // 清空旧数据
                contentListMap.clear();
                for (const [key, value] of Object.entries(res.result)) {
                    contentListMap.set(key, value);
                }
            }
        };

        // 初始化坑位元素内容
        const initCellItemData = async () => {
            await Promise.all([
                // 获取楼层内容数据
                getSectionContent(),

                // 获取内容数据
                getContent(),
            ]);
        };

        // 批量转换 PageCellItem 为 CellItemData
        const batchConvertToCellItemData = (sectionCodes?: string[]) => {
            const pageSectionList = page.value.pageSectionList;
            if (!pageSectionList) return;

            // 如果指定了楼层编码，只处理这些楼层；否则处理所有楼层（兼容旧逻辑）
            const sectionsToProcess = sectionCodes
                ? pageSectionList.filter((section) => sectionCodes.includes(section.code))
                : pageSectionList;

            for (const pageSection of sectionsToProcess) {
                const { pageCellList, dsCode } = pageSection;
                if (!pageCellList) continue;

                // 预先获取 contentList，避免重复查询
                const contentList = contentListMap.get(dsCode) || [];
                let contentIndex = 0;

                for (const pageCell of pageCellList) {
                    const { code, dsParams, pageCellItemList } = pageCell;
                    const size = dsParams?.size ?? 1;

                    // 判断数据优先级
                    const isPageSectionData = getContentDataPriority(pageSection, pageCell) === 'pageSection';

                    if (isPageSectionData) {
                        // 页面楼层数据处理
                        const cellItemDataList = new Array(size);

                        for (let j = 0; j < size; j++) {
                            const content = contentList[contentIndex++];
                            cellItemDataList[j] = {
                                ...convertContentDataToCellItemData(pageCell, content),
                                content,
                            };
                        }

                        cellContentMap.set(code, cellItemDataList);
                    } else if (pageCellItemList) {
                        // 坑位数据处理 - 只遍历一次
                        const cellItemDataList = new Array(pageCellItemList.length);

                        for (let i = 0; i < pageCellItemList.length; i++) {
                            const pageCellItem = pageCellItemList[i];
                            const content = contentMap.get(pageCellItem.dataCode);

                            // 创建或更新 cellItemData
                            const cellItemData = pageCellItem.itemData || {};
                            if (content) {
                                cellItemData.content = content;
                            }

                            // 更新引用
                            pageCellItem.itemData = cellItemData;
                            cellItemDataList[i] = cellItemData;
                        }

                        cellContentMap.set(code, cellItemDataList);
                    }
                }

                // 标记该楼层已处理
                processedSections.value.add(pageSection.code);
            }
        };

        // ContentData 转为 CellItemData
        const convertContentDataToCellItemData = (pageCell: PageCell, contentData: ContentData) => {
            const cellItemData: CellItemData = {
                itemTitle: contentData?.title,
                itemSubTitle: contentData?.subTitle,
                itemType: contentData?.type,
                itemCode: contentData?.code,
                itemIcons: convertIcons(pageCell.layout.rect.width, pageCell.layout.rect.height, contentData?.pictures),
                dataLinkType: '',
                dataLink: '',
                cornerMarks: convertCornerMarks(contentData?.cornerMarks),
                channelCode: '',
                startTime: '',
                endTime: '',
                content: contentData ?? {},
            };
            return cellItemData;
        };

        // 转换 icon
        const convertIcons = (width: number, height: number, pictures: Record<string, any> | undefined) => {
            if (!pictures) {
                return {};
            }

            return {
                ...pictures,
                icon: width > height ? pictures['hImg'] : pictures['vImg'],
            };
        };

        // 角标转换
        const convertCornerMarks = (cornerMarks: Record<string, any> | undefined) => {
            if (!cornerMarks) {
                return [];
            }
            const baseMarkCode = cornerMarks.baseMark;
            const opMarkCode = cornerMarks.opMark;
            const codes = [baseMarkCode, opMarkCode].filter(Boolean) as string[];
            return cornerMarkStore.getCornerMarkByCodes(codes);
        };

        // 根据页面楼层、坑位获取优先级
        const getContentDataPriority = (pageSection: PageSection, cell: PageCell) => {
            // 如果配置了页面楼层数据源
            if (pageSection.dsCode && pageSection.dsType) {
                // 且设置了坑位手动配置，就使用坑位自己的数据
                if (cell.dsType === 'cell') {
                    return 'cell';
                }

                // 且没有设置坑位手动配置，就使用页面楼层的数据
                return 'pageSection';
            }

            // 如果没有配置页面楼层数据源，就使用坑位自己的数据
            return 'cell';
        };

        // 更新页面楼层排序
        const updatePageSectionOrderNo = async () => {
            const res = await pageSectionApi.updatePageSectionOrder(page.value.pageSectionList);
            if (res.code === 200) {
                feedback.success('保存页面楼层排序成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('保存页面楼层排序失败：' + res.msg);
            }
            return res;
        };

        // 更新页面数据
        const refreshPageData = async (code: string, isFullRefresh: boolean = true) => {
            await getPageDetails(code);

            if (isFullRefresh) {
                // 完整刷新
                buildElementMap();
                await initCellItemData();
                batchConvertToCellItemData();
            } else {
                // 初始加载，只处理前几个楼层
                buildElementMap();
                await initCellItemData();

                // 初始只处理前5个楼层
                const initialSections = page.value.pageSectionList?.slice(0, 5) || [];
                const initialSectionCodes = initialSections.map((section) => section.code);

                // 构建初始楼层的元素映射
                buildElementMap(initialSectionCodes);

                // 只转换初始楼层的数据
                batchConvertToCellItemData(initialSectionCodes);

                // 标记这些楼层为可见
                visibleSectionCodes.value = new Set(initialSectionCodes);
            }
        };

        // 刷新页面
        const refreshPage = async (code: string) => {
            // 清理之前的观察器
            stopSectionObserver();

            // 清空已处理标记
            processedSections.value.clear();

            await refreshPageData(code, false);

            if (mode.value === 'desktop') {
                switchElement('Desktop', code);
            } else {
                switchElement('Page', code);
            }

            // 初始化
            leftPanelActiveTab.value = 'section';
            rightPanelActiveTab.value = 'props';

            batchEditMode.value = false;
            batchEditCells.value = [];

            // 启动新的观察器
            startSectionObserver();
        };

        // 更新样式信息
        const updateProps = (value: Record<string, any>) => {
            selectedElement.value.layout.props = value;
        };

        // 保存样式信息
        const saveProps = async () => {
            let res: any;
            if (selectedElement.value.componentType === 'page') {
                res = await pageApi.updatePageLayout(selectedElement.value.code, selectedElement.value as Page);
            } else if (selectedElement.value.componentType === 'section') {
                res = await pageSectionApi.updatePageSectionLayout(
                    selectedElement.value.code,
                    selectedElement.value as PageSection
                );
            } else {
                res = await cellApi.updatePageCellLayout(selectedElement.value.code, selectedElement.value as PageCell);
            }

            if (res && res.code === 200) {
                feedback.success('更新样式成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新样式失败：' + res.msg);
            }
        };

        // 更新元素信息
        const updateElement = async () => {
            if (!selectedElementCode.value) {
                return;
            }

            let res: any;
            switch (selectedElementType.value) {
                case 'Desktop':
                    res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                    break;
                case 'Page':
                    res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                    break;
                case 'PageSection':
                    res = await pageSectionApi.updatePageSectionData(selectedElementCode.value, selectedElement.value);
                    break;
                case 'Cell':
                    res = await cellApi.updatePageCellData(selectedElementCode.value, selectedElement.value);
                    break;
            }

            if (res && res.code === 200) {
                feedback.success('更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新失败');
            }

            return res;
        };

        // 批量更新坑位样式信息
        const batchUpdateCellLayoutProps = async (
            componentStyleCode: string,
            pageCells: PageCell[],
            value: Record<string, any>
        ) => {
            pageCells.forEach((pageCell) => {
                pageCell.layout.props = value;
            });

            // 将 pageCells 映射为codeLayoutMap
            const codeLayoutMap: Record<string, LayoutFile> = {};
            pageCells.forEach((pageCell) => {
                codeLayoutMap[pageCell.code] = pageCell.layout;
            });

            const res = await cellApi.batchUpdatePageCellLayout(componentStyleCode, codeLayoutMap);
            if (res.code === 200) {
                feedback.success('批量更新坑位样式成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('批量更新坑位样式失败：' + res.msg);
            }
        };

        // 更新坑位组件
        const updateCellComponent = async (componentStyle: ComponentStyle, sync: boolean) => {
            if (!selectedElementCode.value) {
                return;
            }

            // 更新组件类型、样式 props
            selectedElement.value.componentType = componentStyle.type;
            selectedElement.value.componentStyleCode = componentStyle.code;

            // 需要遍历 componentStyle.layout 的所有属性
            selectedElement.value.layout.props = {
                ...selectedElement.value.layout.props,
                ...Object.values(componentStyle.layout).reduce((acc, obj) => ({ ...acc, ...obj }), {}),
            };

            // 调用接口
            if (!sync) {
                return;
            }
            const res = await cellApi.updatePageCellComponent(selectedElementCode.value, selectedElement.value);
            if (res.code === 200) {
                feedback.success('更新坑位组件成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新坑位组件失败：' + res.msg);
            }
        };

        // 切换模式
        const switchMode = (modeForm: 'page' | 'desktop') => {
            mode.value = modeForm;
        };

        // 设置页面所属的导航和导航所属的桌面
        const setBizGroupDesktopNav = async (bizGroupForm: string, desktopCodeForm: string, navCodeForm: string) => {
            bizGroup.value = bizGroupForm;
            desktopCode.value = desktopCodeForm;
            navCode.value = navCodeForm;

            // 根据桌面编码查询桌面实体
            await getDesktop();
        };

        // 切换元素
        const switchElement = (type: ElementType, code: string) => {
            selectedElementType.value = type;
            selectedElementCode.value = code;
        };

        // 切换坑位元素
        const switchPageCellItem = (itemCode: string) => {
            if (selectedPageCell.value) {
                selectedCellItemMap.set(selectedPageCell.value.code, itemCode);
            }
        };

        // 根据坑位编码获取选中的坑位元素
        const getSelectedCellItemData = computed(() => {
            // 返回一个函数，但这个函数是在 computed 内部的
            return (pageCellCode: string): CellItemData[] | CellItemData | null | undefined => {
                const pageCell: PageCell = elementMap.get(pageCellCode);
                if (!pageCell) {
                    return null;
                }

                let cellItemDataList: CellItemData[] = cellContentMap.get(pageCellCode) ?? [];

                // 这里访问 reactive 的 Map，会被 Vue 追踪到
                let itemCode = selectedCellItemMap.get(pageCellCode);

                // 如果没有用户选择，找默认元素或第一个元素
                if (!itemCode) {
                    const defaultItem = pageCell.pageCellItemList?.find(
                        (item: PageCellItem) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT
                    );
                    itemCode = defaultItem?.code || pageCell.pageCellItemList?.[0]?.code;
                }

                // 获取到最终的坑位元素编码，根据编码获取坑位元素
                if (!itemCode) {
                    return null;
                }

                // 需要根据坑位的 dsParam.size 决定返回几个数据
                const size = pageCell?.dsParams?.size ?? 1;
                if (size > 1) {
                    // 返回 size 个 pageCellItem
                    return cellItemDataList.slice(0, size) ?? [];
                }

                const pageCellItem = pageCell.pageCellItemList?.find((item: PageCellItem) => item.code === itemCode);
                return (
                    cellItemDataList?.find(
                        (cellItemData: CellItemData) => cellItemData.itemCode === pageCellItem?.dataCode
                    ) ?? cellItemDataList[0]
                );
            };
        });

        // 切换是否展示预删除的页面楼层
        const switchDeletedPageSections = () => {
            showDeletedPageSections.value = !showDeletedPageSections.value;
        };

        // 新增页面楼层
        const addPageSection = async (sourceCode: string, targetCode: string) => {
            const res = await pageSectionApi.addPageSection(sourceCode, targetCode);
            if (res.code === 200) {
                feedback.success('新增页面楼层成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('新增页面楼层失败：' + res.msg);
            }
        };

        // 引用页面楼层
        const refPageSection = async (sourceCode: string, targetCode: string) => {
            const res = await pageSectionApi.refPageSection(sourceCode, targetCode);
            if (res.code === 200) {
                feedback.success('引用页面楼层成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('引用页面楼层失败：' + res.msg);
            }
        };

        // 更换页面楼层
        const changePageSection = async (sourceCode: string, targetCode: string) => {
            const res = await pageSectionApi.changePageSection(sourceCode, targetCode);
            if (res.code === 200) {
                feedback.success('更换页面楼层成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('更换页面楼层失败：' + res.msg);
            }
        };

        // 页面楼层另存为楼层定义
        const pageSectionSaveAsSection = async (pageSectionCode: string, sectionForm: Section) => {
            const res = await pageSectionApi.saveAsSection(pageSectionCode, sectionForm);
            if (res.code === 200) {
                feedback.success('页面楼层另存为楼层定义成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('页面楼层另存为楼层定义失败：' + res.msg);
            }
        };

        // 滚动模式下：新增坑位
        const createPageCell = async (pageSectionCode: string) => {
            // 拿到楼层对应的坑位列表
            const pageSection: PageSection = elementMap.get(pageSectionCode);
            const pageCellList = pageSection.pageCellList || [];

            // 找到 layout.rect.left 最大的坑位
            let maxLeftCell = pageCellList[0];
            if (pageCellList.length > 0) {
                maxLeftCell = pageCellList.reduce((prev, current) => {
                    return prev.layout.rect.left > current.layout.rect.left ? prev : current;
                });
            }

            // 构建新坑位数据对象
            const newCellData: Partial<PageCell> = {
                pageId: pageSection.pageId,
                pageCode: pageSection.pageCode,
                pageSectionId: pageSection.id,
                pageSectionCode: pageSection.code,
                orgId: pageSection.orgId,
                // 如果存在最大 left 的坑位，则基于它创建新的坑位布局
                layout: maxLeftCell
                    ? {
                          rect: {
                              ...maxLeftCell.layout.rect,
                              left:
                                  maxLeftCell.layout.rect.left +
                                  (pageSection.layout.gap ?? 0) +
                                  (pageSection.layout.standardSize?.width ?? maxLeftCell.layout.rect.width),
                          },
                          padding: pageSection.layout.padding,
                          gap: pageSection.layout.gap,
                          mode: pageSection.layout.mode,
                          standardSize: pageSection.layout.standardSize,
                          props: {},
                      }
                    : {
                          rect: { top: 0, left: 0, width: 0, height: 0 },
                          padding: { top: 0, left: 0, right: 0, bottom: 0 },
                          gap: 0,
                          mode: DesignMode.SCROLLABLE,
                          standardSize: { width: 0, height: 0 },
                          props: {},
                      },
            };

            const res = await cellApi.createPageCell(newCellData);
            if (res.code === 200) {
                feedback.success('坑位新增成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位新增失败：' + res.msg);
            }
        };

        // 新增坑位元素
        const createCellItem = async (pageCellItem: PageCellItem) => {
            const res = await cellApi.createPageCellItem(pageCellItem);
            if (res.code === 200) {
                feedback.success('坑位元素新增成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位元素新增失败：' + res.msg);
            }
            return res;
        };

        // 更新坑位元素
        const updateCellItem = async (pageCellItem: PageCellItem) => {
            const res = await cellApi.updatePageCellItem(pageCellItem.code, pageCellItem);
            if (res.code === 200) {
                feedback.success('坑位元素更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位元素更新失败：' + res.msg);
            }
            return res;
        };

        // 确保页面楼层有默认频次
        const ensureDefaultScheduleRule = (pageSection: PageSection) => {
            if (!pageSection.scheduleRule) {
                pageSection.scheduleRule = EMPTY_SCHEDULE_RULE as unknown as ScheduleRule;
            }
        };

        // 将同步操作改为异步，避免阻塞主线程
        const batchEnsureDefaultCellItemAsync = async (pageCells: PageCell[]) => {
            // 先过滤出真正需要处理的坑位
            const cellsNeedingDefault = pageCells.filter((pageCell) => {
                if (!pageCell.pageCellItemList) {
                    pageCell.pageCellItemList = [];
                    return true;
                }
                return !pageCell.pageCellItemList.some((item) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT);
            });

            if (cellsNeedingDefault.length === 0) return;

            const BATCH_SIZE = 50;
            const timestamp = Date.now();

            for (let i = 0; i < cellsNeedingDefault.length; i += BATCH_SIZE) {
                const batch = cellsNeedingDefault.slice(i, i + BATCH_SIZE);

                // 使用 requestIdleCallback 优化
                await new Promise((resolve) => {
                    if ('requestIdleCallback' in window) {
                        requestIdleCallback(() => resolve(undefined), { timeout: 16 });
                    } else {
                        setTimeout(resolve, 0);
                    }
                });

                batch.forEach((pageCell, index) => {
                    pageCell.pageCellItemList.push({
                        ...DEFAULT_ITEM_TEMPLATE,
                        icons: {},
                        code: `item_${timestamp}-${i + index}`,
                        cellId: pageCell.id,
                        cellCode: pageCell.code,
                        scheduleRule: EMPTY_SCHEDULE_RULE,
                    } as unknown as PageCellItem);
                });
            }
        };

        // 确保坑位有默认坑位元素
        const ensureDefaultCellItem = (pageCell: PageCell) => {
            const hasDefaultItem = pageCell.pageCellItemList?.some(
                (item) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT
            );

            if (!hasDefaultItem) {
                if (!pageCell.pageCellItemList) {
                    pageCell.pageCellItemList = [];
                }

                pageCell.pageCellItemList.push({
                    ...DEFAULT_ITEM_TEMPLATE,
                    code: `item_${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                    cellId: pageCell.id,
                    cellCode: pageCell.code,
                    scheduleRule: EMPTY_SCHEDULE_RULE,
                } as unknown as PageCellItem);
            }
        };

        // 对坑位元素进行排序（默认项在前）
        const sortCellItems = (pageCell: PageCell) => {
            if (!pageCell.pageCellItemList || pageCell.pageCellItemList.length <= 1) return;

            pageCell.pageCellItemList.sort((a, b) => {
                if (a.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return -1;
                if (b.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return 1;
                return 0;
            });
        };

        // 基础画布引用
        const baseCanvasRef = ref<any>(null);

        // 页面楼层截图功能
        const capturePageSectionScreenshot = async (pageSection: PageSection) => {
            // 等待 DOM 更新
            await nextTick();

            // 查找对应的页面楼层容器元素
            const targetElement = document.getElementById(`page-section-${pageSection.code}`);
            // 创建临时的 ref 用于截图
            const tempRef = ref<HTMLElement>(targetElement as HTMLElement);

            // 使用 useCanvasDownload 进行截图和上传
            const { uploadToServer } = useCanvasDownload(
                tempRef,
                {
                    format: 'png',
                    backgroundColor: '#ffffff',
                },
                storageApi.uploadFile
            );

            const result = await uploadToServer();
            if (result && result.code === 200) {
                pageSection.icon = result.result;
                const resp = await pageSectionApi.updatePageSectionData(pageSection.code, pageSection);
                if (resp.code === 200) {
                    await refreshPageData(page.value.code);
                }
            }
        };

        // 滚动到指定页面楼层
        const scrollToPageSection = (pageSectionCode: string) => {
            // 先切换元素状态
            switchElement('PageSection', pageSectionCode);

            // 定义滚动执行函数
            const executeScroll = () => {
                const sectionElement = document.getElementById(`section-${pageSectionCode}`);
                if (sectionElement && baseCanvasRef.value) {
                    // 使用 base-canvas 的 updatePositionAndRulers 方法进行滚动
                    baseCanvasRef.value.updatePositionAndRulers(0, -sectionElement.offsetTop);
                }
            };

            // 定义重试逻辑
            const tryScroll = (retryCount = 0) => {
                if (retryCount > 10) return;

                if (!baseCanvasRef.value) {
                    // baseCanvasRef 还没注册，等待后重试
                    setTimeout(() => tryScroll(retryCount + 1), 100);
                    return;
                }

                const sectionElement = document.getElementById(`section-${pageSectionCode}`);
                if (!sectionElement) {
                    // DOM 元素还没渲染，等待后重试
                    setTimeout(() => tryScroll(retryCount + 1), 100);
                    return;
                }

                // 执行滚动
                nextTick(() => {
                    nextTick(executeScroll);
                });
            };

            // 开始尝试滚动
            tryScroll();
        };

        // 注册基础画布引用
        const registerBaseCanvas = (canvas: any) => {
            baseCanvasRef.value = canvas;
        };

        // 页面设计器 tab 切换
        const switchTab = (position: 'left' | 'right', tab: string) => {
            if (position === 'left') {
                leftPanelActiveTab.value = tab;
            } else if (position === 'right') {
                rightPanelActiveTab.value = tab;
            }
        };

        // 批量编辑相关
        const batchEditMode = ref<boolean>(false);
        const batchEditCells = ref<PageCell[]>([]);

        return {
            mode,
            bizGroup,
            desktopCode,
            desktop,
            navCode,
            page,
            elementMap,
            selectedElementCode,
            selectedElementType,
            selectedElement,
            getPageDetails,
            buildElementMap,
            updatePageSectionOrderNo,
            refreshPageData,
            refreshPage,
            updateProps,
            saveProps,
            updateElement,
            batchUpdateCellLayoutProps,
            updateCellComponent,
            switchMode,
            setBizGroupDesktopNav,
            switchElement,
            switchPageCellItem,
            getSelectedCellItemData,
            showDeletedPageSections,
            switchDeletedPageSections,
            changePageSection,
            refPageSection,
            addPageSection,
            getPageSection,
            updatePageSection,
            updatePageCell,
            selectedPageSection,
            selectedPageCell,
            selectedCellItemMap,
            createPageCell,
            createCellItem,
            updateCellItem,
            ensureDefaultCellItem,
            sortCellItems,
            pageSectionSaveAsSection,
            getElementTypeName,
            baseCanvasRef,
            scrollToPageSection,
            registerBaseCanvas,
            capturePageSectionScreenshot,
            leftPanelActiveTab,
            rightPanelActiveTab,
            switchTab,
            batchEditMode,
            batchEditCells,
            startSectionObserver,
            stopSectionObserver,
            visibleSectionCodes,
            processedSections,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'design-portal-store',
            driver: STORAGE_DRIVER.INDEXED_DB,
            storeKey: 'page-designer-store',
            serializer: {
                serialize: (state: StateTree): any => {
                    return {
                        ...state,
                        // 将 Set 转换为数组进行序列化
                        visibleSectionCodes: state.visibleSectionCodes ? Array.from(state.visibleSectionCodes) : [],
                        processedSections: state.processedSections ? Array.from(state.processedSections) : [],
                    };
                },
                deserialize: (data: any): StateTree | any => {
                    const stateTree: StateTree = data;

                    // 恢复 elementMap
                    if (stateTree.elementMap) {
                        stateTree.elementMap = reactive(new Map(Object.entries(stateTree.elementMap)));
                    } else {
                        stateTree.elementMap = reactive(new Map());
                    }

                    // 恢复 selectedCellItemMap
                    if (stateTree.selectedCellItemMap) {
                        stateTree.selectedCellItemMap = reactive(
                            new Map(Object.entries(stateTree.selectedCellItemMap))
                        );
                    } else {
                        stateTree.selectedCellItemMap = reactive(new Map());
                    }

                    // 恢复 contentMap
                    if (stateTree.contentMap) {
                        stateTree.contentMap = reactive(new Map(Object.entries(stateTree.contentMap)));
                    } else {
                        stateTree.contentMap = reactive(new Map());
                    }

                    // 恢复 contentListMap
                    if (stateTree.contentListMap) {
                        stateTree.contentListMap = reactive(new Map(Object.entries(stateTree.contentListMap)));
                    } else {
                        stateTree.contentListMap = reactive(new Map());
                    }

                    // 恢复 cellContentMap
                    if (stateTree.cellContentMap) {
                        stateTree.cellContentMap = reactive(new Map(Object.entries(stateTree.cellContentMap)));
                    } else {
                        stateTree.cellContentMap = reactive(new Map());
                    }

                    // 恢复 Set 类型的数据
                    if (stateTree.visibleSectionCodes) {
                        stateTree.visibleSectionCodes = ref(new Set(stateTree.visibleSectionCodes));
                    } else {
                        stateTree.visibleSectionCodes = ref(new Set());
                    }

                    if (stateTree.processedSections) {
                        stateTree.processedSections = ref(new Set(stateTree.processedSections));
                    } else {
                        stateTree.processedSections = ref(new Set());
                    }

                    return stateTree;
                },
            },
        },
    }
);
