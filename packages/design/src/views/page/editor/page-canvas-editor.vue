<template>
    <div
        ref="containerRef"
        class="relative w-full h-full overflow-auto"
        :class="{
            'ring-2 border-2 border-blue-500 ring-blue-500': pageDesignerStore.selectedElementType === 'Page',
            'ring-2 border-2 border-transparent ring-transparent': pageDesignerStore.selectedElementType !== 'Page',
        }"
        :style="pageStyle"
        @dragover.prevent="onDragOver"
        @drop="onDropSection"
        @dragleave.prevent="onDragLeave">
        <div
            v-for="(pageSection, index) in pageSections"
            :key="index"
            class="relative section-wrapper"
            :id="`section-${pageSection.code}`"
            :data-section-code="pageSection.code">
            <div
                v-if="
                    dragType === 'section' &&
                    dragOverIndex !== null &&
                    Math.floor(dragOverIndex) === index &&
                    dragOverIndex === index
                "
                class="w-full h-16 border-2 border-dashed border-blue-500 flex justify-center items-center bg-white bg-opacity-10 my-4">
                <span class="text-gray-500">放置楼层定义在此处</span>
            </div>

            <div class="section-content">
                <page-section-toolbar-editor
                    class="mb-1"
                    :page-section="pageSection"
                    @add="handleAddSection"
                    @ref-section="handleRefSection"
                    @change="handleChangeSection"
                    @save-as-section="handleSaveAsSection"
                    @publish="handlePublishSection"
                    @publish-all="handlePublishSectionComplete"
                    @delete="handleDeleteSection" />

                <div
                    class="relative w-full cursor-pointer"
                    :class="{
                        'bg-red-200 border-2 border-red-200':
                            pageSection.delFlag !== 0 &&
                            pageDesignerStore.selectedPageSection?.code !== pageSection.code,
                        'bg-red-200 border-2 border-red-500':
                            pageSection.delFlag !== 0 &&
                            pageDesignerStore.selectedPageSection?.code === pageSection.code,
                        'border-2 border-gray-200':
                            pageSection.delFlag === 0 &&
                            pageDesignerStore.selectedPageSection?.code !== pageSection.code,
                        'border-2 border-blue-500':
                            pageSection.delFlag === 0 &&
                            pageDesignerStore.selectedPageSection?.code === pageSection.code,
                    }"
                    @mousedown.stop="handleSwitchSection(pageSection.code)">
                    <div
                        v-if="pageSection.refSectionId && pageSection.refSectionCode"
                        class="absolute inset-0 rounded-lg z-[150] pointer-events-none">
                        <div
                            class="absolute inset-0 bg-gray-700 opacity-40 transition-opacity duration-300 rounded-lg"></div>
                        <div
                            class="relative flex items-center justify-center h-full text-white text-base font-semibold">
                            <div class="flex items-center flex-col gap-1 ref_section_style">
                                <div>引用楼层，不可编排</div>
                                <div>引用楼层名称: {{ pageSection.name }}</div>
                                <div>引用楼层编码: {{ pageSection.refSectionCode }}</div>
                            </div>
                        </div>
                    </div>
                    <page-section-canvas-editor
                        :page-section="pageSection"
                        @delete-cell="handleDeleteCell"
                        @publish-cell="handlePublishCell" />

                    <div
                        v-if="pageDesignerStore.selectedPageSection?.code === pageSection.code"
                        class="absolute inset-0 bg-white opacity-40 transition-opacity duration-300 rounded-lg z-[100] pointer-events-none"></div>

                    <div
                        v-if="isPageSectionBeforeValid(pageSection)"
                        class="absolute inset-0 rounded-lg z-[100] pointer-events-none">
                        <div
                            class="absolute inset-0 bg-gray-600 opacity-40 transition-opacity duration-300 rounded-lg"></div>
                        <div class="relative flex items-center justify-center h-full text-white text-lg font-bold">
                            <div class="flex items-center flex-col gap-2">
                                <div>当前楼层未到生效时间</div>
                                <div>生失效时间：{{ pageSection.validTime }} - {{ pageSection.expireTime }}</div>
                                <div>频次：{{ formatScheduleRule(pageSection.scheduleRule) }}</div>
                            </div>
                        </div>
                    </div>

                    <div v-if="pageSection.delFlag === -1" class="deleted-label z-51">
                        <span>预删除</span>
                    </div>
                </div>
            </div>

            <div
                v-if="
                    dragType === 'section' &&
                    dragOverIndex !== null &&
                    Math.floor(dragOverIndex) === index &&
                    dragOverIndex !== index
                "
                class="w-full h-16 border-2 border-dashed border-blue-500 flex justify-center items-center bg-white bg-opacity-10 my-4">
                <span class="text-gray-500">放置楼层定义在此处</span>
            </div>
        </div>

        <div v-if="pageSections.length === 0" class="w-full flex justify-center items-center min-h-[200px]">
            <div
                v-if="dragType === 'section' && isDraggingOver"
                class="w-full h-16 border-2 border-dashed border-blue-500 flex justify-center items-center bg-white bg-opacity-10">
                <span class="text-gray-500">放置楼层定义在此处</span>
            </div>
            <div v-else class="w-full h-16 flex justify-center items-center bg-white bg-opacity-10">
                <span class="text-gray-500">拖拽楼层定义到此处</span>
            </div>
        </div>

        <div
            v-if="
                dragType === 'section' &&
                dragOverIndex !== null &&
                dragOverIndex === pageSections.length &&
                pageSections.length > 0
            "
            class="w-full h-16 border-2 border-dashed border-blue-500 flex justify-center items-center bg-white bg-opacity-10 my-4">
            <span class="text-gray-500">放置楼层定义在此处</span>
        </div>
    </div>

    <page-section-change-dialog
        v-if="pageSectionChangeVisible"
        v-model:visible="pageSectionChangeVisible"
        :action="pageSectionAction"
        @submit="handleSectionChangeSubmit" />

    <page-section-save-as-section-dialog
        v-if="pageSectionAddVisible"
        v-model:visible="pageSectionAddVisible"
        @submit="handleSectionSaveSubmit" />
</template>

<script setup lang="ts">
    import { computed, CSSProperties, ref } from 'vue';
    import { useFeedback } from '@smartdesk/common/composables';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { cellApi, pageSectionApi, publishApi } from '@smartdesk/common/api';
    import { PageSection, ScheduleRule, Section } from '@smartdesk/common/types';
    import PageSectionToolbarEditor from './page-section-toolbar-editor.vue';
    import PageSectionCanvasEditor from './page-section-canvas-editor.vue';
    import PageSectionChangeDialog from '../dialog/page-section-change-dialog.vue';
    import PageSectionSaveAsSectionDialog from '../dialog/page-section-save-as-section-dialog.vue';

    // 页面设计器页面画布编辑器
    defineOptions({
        name: 'PageCanvasEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const { confirm, success, error } = useFeedback();

    const containerRef = ref<HTMLElement | null>(null);

    // 拖拽状态管理
    const isDraggingOver = ref<boolean>(false);
    const dragType = ref<'section' | 'componentStyle' | null>(null);
    const dragOverIndex = ref<number | null>(null);

    // 页面楼层更换弹窗是否显示
    const pageSectionChangeVisible = ref<boolean>(false);
    const pageSectionAction = ref<'add' | 'change' | 'refSection'>('change');

    // 页面楼层新增弹窗是否显示
    const pageSectionAddVisible = ref<boolean>(false);

    // 页面楼层列表
    const pageSections = computed({
        get: () => {
            return pageDesignerStore.page.pageSectionList.filter(
                (section) =>
                    (pageDesignerStore.showDeletedPageSections && section.delFlag === -1) || section.delFlag === 0
            );
        },
        set: (value) => {
            pageDesignerStore.page.pageSectionList = value;
        },
    });

    // 页面样式
    const pageStyle = computed<CSSProperties>(() => {
        if (pageDesignerStore.page.layout.props && pageDesignerStore.page.layout.props.background) {
            if (pageDesignerStore.page.layout.props.background.type === 'color') {
                // 纯色
                return {
                    backgroundColor: pageDesignerStore.page.layout.props.background.color,
                    opacity: pageDesignerStore.page.layout.props.background.opacity,
                };
            }

            if (pageDesignerStore.page.layout.props.background.type === 'gradient') {
                // 渐变色
                const direction =
                    pageDesignerStore.page.layout.props.background.linearGradient?.direction || 'to bottom';
                const colors = pageDesignerStore.page.layout.props.background.linearGradient?.colors?.join(', ') || '';

                return {
                    background: `linear-gradient(${direction}, ${colors})`,
                    opacity: pageDesignerStore.page.layout.props.background.opacity,
                };
            }

            if (pageDesignerStore.page.layout.props.background.type === 'image') {
                // 图片
                const imageUrl = pageDesignerStore.page.layout.props.background.image
                    ? `url('${pageDesignerStore.page.layout.props.background.image}')`
                    : '';
                return {
                    backgroundImage: `${imageUrl}`,
                    backgroundSize: 'contain',
                    backgroundPosition: 'center center',
                    backgroundRepeat: 'repeat',
                    opacity: pageDesignerStore.page.layout.props.background.opacity,
                };
            }
        }
        return {};
    });

    // 点击页面楼层
    const handleSwitchSection = (code: string) => {
        // 批量模式下，不允许点击页面楼层
        if (pageDesignerStore.batchEditMode) {
            return;
        }
        pageDesignerStore.switchElement('PageSection', code);
    };

    // 判断页面楼层是否未到生效时间
    const isPageSectionBeforeValid = (pageSection: PageSection): boolean => {
        if (!pageSection.validTime) {
            return false;
        }
        const now = new Date();
        const validTime = new Date(pageSection.validTime);
        return now < validTime;
    };

    // 格式化频次输出：每日 开始时间到结束时间、每周 周一···周日 开始时间到结束时间、每月 1号···31号 开始时间到结束时间
    const formatScheduleRule = (scheduleRule: ScheduleRule) => {
        const { scheduleType, dayList } = scheduleRule.scheduleConfig;
        let { startTime, endTime } = scheduleRule.scheduleConfig;
        if (!startTime) {
            startTime = '00:00:00';
        }
        if (!endTime) {
            endTime = '23:59:59';
        }

        if (scheduleType === 0) {
            return `(每日) ${startTime} - ${endTime}`;
        }
        if (scheduleType === 1) {
            // 阿拉伯数字转中文
            const weekMap = new Map([
                [1, '一'],
                [2, '二'],
                [3, '三'],
                [4, '四'],
                [5, '五'],
                [6, '六'],
                [7, '日'],
            ]);
            const days = dayList
                .sort()
                .map((day) => weekMap.get(day))
                .join('、');
            return `(每周${days}) ${startTime} - ${endTime}`;
        }
        if (scheduleType === 2) {
            const days = dayList
                .sort()
                .map((day) => `${day}号`)
                .join('、');
            return `(每月${days}) ${startTime} - ${endTime}`;
        }
        return '';
    };

    // 处理拖拽悬停
    const onDragOver = (event: DragEvent) => {
        if (!containerRef.value) return;

        isDraggingOver.value = true;
        const types = event.dataTransfer?.types || [];

        if (types.some((type) => type.toLowerCase().includes('section'))) {
            dragType.value = 'section';
            const mouseY = event.clientY;
            const sectionWrappers = containerRef.value.querySelectorAll('.section-wrapper');
            let insertIndex: number | null = null;
            for (let i = 0; i < sectionWrappers.length; i++) {
                const rect = sectionWrappers[i].getBoundingClientRect();
                const midY = (rect.top + rect.bottom) / 2;
                if (mouseY < midY) {
                    insertIndex = i;
                    break;
                }
            }
            if (insertIndex === null) {
                insertIndex = pageSections.value.length;
            }
            dragOverIndex.value = insertIndex;
        } else if (types.some((type) => type.toLowerCase().includes('componentstyle'))) {
            dragType.value = 'componentStyle';
            dragOverIndex.value = null;
        }
    };

    // 拖拽离开处理
    const onDragLeave = (event: DragEvent) => {
        if (!containerRef.value) return;

        // 容器绝对定位
        const rect = containerRef.value.getBoundingClientRect();

        // 检查鼠标是否真的离开了容器区域
        if (
            event.clientX < rect.left ||
            event.clientX > rect.right ||
            event.clientY < rect.top ||
            event.clientY > rect.bottom
        ) {
            isDraggingOver.value = false;
            dragOverIndex.value = null;
            dragType.value = null;
        }
    };

    // 拖拽放在楼层定义
    const onDropSection = async (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();

        // 获取插入位置
        const targetIndex = dragOverIndex.value;
        const currentDragType = dragType.value;

        // 重置拖拽状态
        isDraggingOver.value = false;
        dragOverIndex.value = null;
        dragType.value = null;

        if (currentDragType === 'section') {
            // 楼层定义拖拽处理
            const sectionStr = event.dataTransfer?.getData('section');
            if (sectionStr) {
                const section: Section = JSON.parse(sectionStr);

                // 获取前一个楼层
                const prevSection =
                    targetIndex === null || targetIndex === 0 ? null : pageSections.value[targetIndex - 1];
                const position = prevSection ? prevSection.orderNo : 0;

                // 新增楼层
                const res = await pageSectionApi.createPageSection(pageDesignerStore.page.code, section, position);
                if (res.code === 200) {
                    success('新增楼层成功');
                    // 刷新页面
                    await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);

                    // 截图
                    await pageDesignerStore.capturePageSectionScreenshot(res.result);
                } else {
                    error('新增楼层失败：' + res.msg);
                }
                return;
            }
        }
    };

    // 处理新增楼层
    const handleAddSection = (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }

        pageSectionChangeVisible.value = true;
        pageSectionAction.value = 'add';
    };
    // 处理引用楼层
    const handleRefSection = (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }

        pageSectionChangeVisible.value = true;
        pageSectionAction.value = 'refSection';
    };

    // 处理更换楼层
    const handleChangeSection = (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }

        pageSectionChangeVisible.value = true;
        pageSectionAction.value = 'change';
    };

    // 处理更换/新增楼层提交事件
    const handleSectionChangeSubmit = async (pageSection: PageSection) => {
        const confirmMsg = getConfirmMsg(pageSectionAction.value, pageSection);
        const confirmRes = await confirm(confirmMsg);
        if (confirmRes && pageDesignerStore.selectedElementCode) {
            if (pageSectionAction.value === 'add') {
                await pageDesignerStore.addPageSection(pageDesignerStore.selectedElementCode, pageSection.code);
            } else if (pageSectionAction.value === 'change') {
                await pageDesignerStore.changePageSection(pageDesignerStore.selectedElementCode, pageSection.code);
            } else if (pageSectionAction.value === 'refSection') {
                await pageDesignerStore.refPageSection(pageDesignerStore.selectedElementCode, pageSection.code);
            }
        }
    };

    // 获取确认消息
    const getConfirmMsg = (pageSectionAction: string, pageSection: PageSection) => {
        if (pageSectionAction === 'add') {
            return (
                '确定在楼层 ' + pageDesignerStore.selectedElement?.name + ' 下新增楼层 ' + pageSection.name + ' 吗？'
            );
        } else if (pageSectionAction === 'change') {
            return '确定更换楼层 ' + pageDesignerStore.selectedElement?.name + ' 为 ' + pageSection.name + ' 吗？';
        } else if (pageSectionAction === 'refSection') {
            return (
                '确定引用楼层 ' + pageDesignerStore.selectedElement?.name + ' 下引用楼层 ' + pageSection.name + ' 吗？'
            );
        }
        return '';
    };

    // 处理另存为楼层定义提交事件
    const handleSectionSaveSubmit = async (sectionForm: Section) => {
        if (await confirm('确定将页面楼层：' + pageDesignerStore.selectedPageSection.name + ' 另存为楼层定义？')) {
            await pageDesignerStore.pageSectionSaveAsSection(pageDesignerStore.selectedPageSection.code, sectionForm);
        }
        pageSectionAddVisible.value = false;
    };

    // 处理送审楼层
    const handlePublishSection = async (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }

        const confirmRes = await confirm('确定送审楼层：' + pageSection.name + '吗？');
        if (confirmRes) {
            const res =
                pageSection.delFlag === -1
                    ? await publishApi.publishComplete('PageSection', pageSectionCode, 'DELETE')
                    : await publishApi.publishSelf('PageSection', pageSectionCode, 'CREATE');
            if (res.code === 200) {
                success('送审楼层：' + pageSection.name + '成功');
                // 刷新页面以更新UI
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                error('送审楼层：' + pageSection.name + '失败：' + res.msg);
            }
        }
    };

    // 处理全发送审楼层
    const handlePublishSectionComplete = async (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }

        const confirmRes = await confirm('确定全部送审楼层：' + pageSection.name + '吗？');
        if (confirmRes) {
            const res = await publishApi.publishComplete('PageSection', pageSectionCode, 'CREATE');
            if (res.code === 200) {
                success('全部送审楼层：' + pageSection.name + '成功');
                // 刷新页面以更新UI
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                error('全部送审楼层：' + pageSection.name + '失败：' + res.msg);

                // 从 msg 中提取
                const matches = [...res.msg.matchAll(/\[(.*?)\]/g)];
                const codes = matches ? matches.map((match) => match[1]) : [];
                if (codes && codes.length > 1) {
                    pageDesignerStore.scrollToPageSection(codes[0]);
                    pageDesignerStore.switchElement('Cell', codes[1]);
                }
            }
        }
    };

    // 处理页面楼层另存为楼层定义
    const handleSaveAsSection = async (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        pageSectionAddVisible.value = true;
    };

    // 处理删除页面楼层
    const handleDeleteSection = async (pageSectionCode: string) => {
        handleSwitchSection(pageSectionCode);
        const pageSection: PageSection = pageDesignerStore.elementMap.get(pageSectionCode);
        if (!pageSection) {
            return;
        }
        if (await confirm('确定删除页面楼层：' + pageSection.name + ' 吗？')) {
            const res = await publishApi.publishComplete('PageSection', pageSectionCode, 'DELETE');
            if (res.code === 200) {
                success('楼层已删除成功，并送审');
                // 刷新页面
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                error('删除楼层失败：' + res.msg);
            }
        }
    };

    // 处理删除坑位
    const handleDeleteCell = async (sectionCode: string, cellCode: string) => {
        // 找到对应的页面楼层和坑位
        const section = pageSections.value.find((s) => s.code === sectionCode);
        if (!section) return;

        // 从楼层的坑位列表中移除该坑位
        const cellIndex = section.pageCellList.findIndex((c) => c.code === cellCode);
        if (cellIndex === -1) return;

        const cell = section.pageCellList[cellIndex];

        // 提示用户确认删除
        const confirmResult = await confirm(`确定要删除坑位"${cell.name || cellCode}"吗？此操作不可恢复！`);
        if (confirmResult) {
            // 从楼层的坑位列表中移除该坑位
            section.pageCellList.splice(cellIndex, 1);

            // 调用API删除坑位
            // 如果有坑位元素，需要先删除坑位元素
            if (cell.pageCellItemList?.length) {
                const itemCodes = cell.pageCellItemList.map((item) => item.code);
                await cellApi.deletePageCellItems(itemCodes);
            }

            // 刷新页面以更新UI
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            success('坑位删除成功');
        }
    };

    // 处理送审坑位
    const handlePublishCell = async (sectionCode: string, cellCode: string) => {
        // 找到对应的页面楼层和坑位
        const section = pageSections.value.find((s) => s.code === sectionCode);
        if (!section) return;

        // 找到该坑位
        const cell = section.pageCellList.find((c) => c.code === cellCode);
        if (!cell) return;

        // 提示用户确认送审
        const confirmResult = await confirm(`确定要送审坑位"${cell.name || cellCode}"吗？`);
        if (confirmResult) {
            // 调用API送审坑位
            const res = await publishApi.publishSelf('PageCell', cellCode, 'CREATE');
            if (res.code === 200) {
                success('坑位送审成功');
                // 刷新页面以更新UI
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                error('坑位送审失败：' + res.msg);
            }
        }
    };
</script>

<style scoped>
    .deleted-label {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
    }

    .deleted-label span {
        background-color: rgb(254, 226, 226);
        color: rgb(239, 68, 68);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    :deep(.el-button--small) {
        transform-origin: center center;
    }

    .section-wrapper {
        position: relative;
        width: 100%;
    }

    .section-content {
        display: flex;
        flex-direction: column;
        gap: 0;
        margin: 0;
        padding: 0;
        position: relative;
    }

    .section-content > div:last-child {
        margin-top: 0;
        position: relative;
        z-index: 40;
    }

    .section-wrapper:not(:last-child)::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        right: 0;
        height: 1px;
        background-color: rgba(0, 0, 0, 0.05);
    }
    .ref_section_style {
        font-size: 24px;
    }
</style>
