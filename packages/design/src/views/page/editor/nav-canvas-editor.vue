<template>
    <div :class="['bg-white border border-gray-200 flex items-center justify-center', positionClass]">
        <span>导航区域（{{ position }}，可扩展）</span>
    </div>
</template>

<script setup lang="ts">
    import { computed, defineProps } from 'vue';

    const props = defineProps<{ position: 'top' | 'left' | 'right' | 'bottom' }>();
    const positionClass = computed(() => {
        switch (props.position) {
            case 'top':
                return 'w-full h-full';
            case 'left':
                return 'h-full w-full';
            case 'right':
                return 'h-full w-full';
            case 'bottom':
                return 'w-full h-full';
            default:
                return '';
        }
    });
</script>
