<template>
    <top-section-canvas-editor :style="topSectionStyle" v-if="showDesktopStuff" />
    <nav-canvas-editor :style="navStyle" :position="navPosition" v-if="showDesktopStuff" />
    <div :style="pageStyle">
        <page-canvas-editor  />
    </div>
</template>

<script setup lang="ts">
    import { computed, CSSProperties, onMounted, ref } from 'vue';
    import TopSectionCanvasEditor from './top-section-canvas-editor.vue';
    import NavCanvasEditor from './nav-canvas-editor.vue';
    import PageCanvasEditor from './page-canvas-editor.vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import LayoutJson from './layout.json';

    // 桌面画布编辑器
    defineOptions({
        name: 'DesktopCanvasEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 展示桌面相关
    const showDesktopStuff = computed(() => {
        return pageDesignerStore.mode === 'page' && pageDesignerStore.desktopCode && true;
    });

    // 导航位置
    const navPosition = ref<'top' | 'left' | 'right' | 'bottom'>('left');

    // 顶部楼层样式
    const topSectionStyle = computed<CSSProperties>(() => {
        return {
            position: 'absolute',
            top: `${LayoutJson.sections.top.rect.top}px`,
            left: `${LayoutJson.sections.top.rect.left}px`,
            width: `${LayoutJson.sections.top.rect.width}px`,
            height: `${LayoutJson.sections.top.rect.height}px`,
        };
    });

    // 导航样式
    const navStyle = computed<CSSProperties>(() => {
        return {
            position: 'absolute',
            top: `${LayoutJson.sections.nav.rect.top}px`,
            left: `${LayoutJson.sections.nav.rect.left}px`,
            width: `${LayoutJson.sections.nav.rect.width}px`,
            height: `${LayoutJson.sections.nav.rect.height}px`,
        };
    });

    // 页面样式
    const pageStyle = computed<CSSProperties>(() => {
        return {
            position: 'absolute',
            top: `${LayoutJson.sections.page.rect.top}px`,
            left: `${LayoutJson.sections.page.rect.left}px`,
            width: `${LayoutJson.sections.page.rect.width}px`,
        };
    });

    onMounted(() => {
        console.log('DesktopCanvasEditor', pageDesignerStore.desktop);
    });
</script>
