<template>
    <div class="flex flex-col h-full">
        <div class="flex justify-end" style="height: 24px" v-if="showSaveButton && !pageDesignerStore.batchEditMode">
            <el-button
                :disabled="
                    !hasPermission || isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell)
                "
                @click="handleSaveClick"
                type="primary"
                size="small">
                保存
            </el-button>
        </div>
        <div class="flex justify-between" v-if="pageDesignerStore.batchEditMode">
            <div class="mr-2 text-lg">已选择 {{ pageDesignerStore.batchEditCells.length }} 个坑位</div>
            <div>
                <el-button @click="onClickCancelBatchEditMode">取消</el-button>
                <el-button type="primary" @click="onClickConfirmBatchEdit">确定</el-button>
            </div>
        </div>

        <div class="w-full mt-2" :style="scrollContainerStyle">
            <el-scrollbar height="100%">
                <el-form-item v-if="pageDesignerStore.selectedElementType === 'Cell'" label="组件样式">
                    <component-style-selector
                        :disabled="
                            !hasPermission ||
                            isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell)
                        "
                        v-model="selectedComponentStyleCode"
                        @select="handleComponentStyleSelect"
                        class="w-full" />
                </el-form-item>

                <collapse-wrapper v-model="activeNames" :accordion="true" ref="containerRef">
                    <collapse-item-wrapper
                        id="attrs"
                        name="attrs"
                        title="属性"
                        v-if="metaInfo && metaInfo.attrs && metaInfo.attrs.length > 0">
                        <base-editor
                            :disabled="
                                !hasPermission ||
                                isRefSection(pageDesignerStore.selectedPageSection) ||
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell)
                            "
                            :metaInfos="metaInfo.attrs"
                            v-model:values="elementProps" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper
                        id="controls"
                        name="controls"
                        title="控制"
                        v-if="metaInfo.controls && metaInfo.controls.length > 0">
                        <base-editor
                            :disabled="
                                !hasPermission ||
                                isRefSection(pageDesignerStore.selectedPageSection) ||
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell)
                            "
                            :metaInfos="metaInfo.controls"
                            v-model:values="elementProps" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper
                        id="elements"
                        name="elements"
                        title="样式"
                        v-if="metaInfo.elements && metaInfo.elements.length > 0">
                        <base-editor
                            :disabled="
                                !hasPermission ||
                                isRefSection(pageDesignerStore.selectedPageSection) ||
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell)
                            "
                            :metaInfos="metaInfo.elements"
                            v-model:values="elementProps" />
                    </collapse-item-wrapper>
                </collapse-wrapper>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { cellApi, pageApi, pageSectionApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import { ComponentStyle, Page, PageCell, PageSection } from '@smartdesk/common/types';
    import {
        canEdit,
        COMMON_ACTION,
        ENTITY,
        isRefSection,
        isRefSectionCellItem,
        PERMISSION_PREFIX,
        SPECIAL_ACTION,
        SPLITTER,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import BaseEditor from '@smartdesk/design/components/editor/base-editor.vue';
    import ComponentStyleSelector from '@smartdesk/design/views/section/selector/component-style-selector.vue';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useComponentResolver } from '@smartdesk/design/composables';

    // 通用属性编辑器
    defineOptions({
        name: 'PropsEditor',
    });

    // 参数
    const componentProps = defineProps({
        // 是否显示保存按钮
        showSaveButton: {
            type: Boolean,
            default: true,
        },
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const siteStore = useSiteStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 展示项
    const activeNames = ref<string[]>(['attrs', 'controls', 'elements']);

    // 元素属性值
    const elementProps = ref<Record<string, any>>({});

    // 容器引用
    const containerRef = ref<HTMLElement | null>(null);

    // 选中的组件样式
    const selectedComponentStyleCode = ref<string>('');

    // 元数据信息
    const metaInfo = computed<any>(() => {
        return useComponentResolver().resolveComponentManifest(pageDesignerStore.selectedElement?.componentType || '');
    });

    // 滚动容器样式
    const scrollContainerStyle = computed(() => {
        return componentProps.showSaveButton ? 'height: calc(100% - 40px); overflow: auto' : 'height: 100%;';
    });

    // 权限检查
    const hasPermission = computed(() => {
        // 确定元素类型和对应的操作类型
        const isCellType = pageDesignerStore.selectedElementType === 'Cell';
        const actionType = isCellType ? SPECIAL_ACTION.EDIT_STYLE : COMMON_ACTION.EDIT;

        // 构建权限键
        const permissionKey = `${PERMISSION_PREFIX}${SPLITTER}${getLowerCaseElementType()}${SPLITTER}${actionType}`;

        // 权限上下文
        const permissionContext = {
            type: 'org',
            value: pageDesignerStore.selectedElement?.orgId,
        };

        // 检查权限
        const hasEditPermission = permissionStore.hasPermission(permissionKey, permissionContext);

        // 有编辑权限、canEdit、网站配置允许编辑组件样式
        return (
            canEdit(pageDesignerStore.selectedElement) &&
            hasEditPermission &&
            siteStore.currentSite?.config?.canEditComponentStyle
        );
    });

    // 处理组件样式选择
    const handleComponentStyleSelect = async (componentStyle: ComponentStyle) => {
        // 更新坑位组件样式
        await pageDesignerStore.updateCellComponent(componentStyle, false);
    };

    // 处理点击保存
    const handleSaveClick = async () => {
        pageDesignerStore.updateProps(elementProps.value);

        let res: any;
        if (pageDesignerStore.selectedElement.componentType === 'page') {
            res = await pageApi.updatePageLayout(
                pageDesignerStore.selectedElement.code,
                pageDesignerStore.selectedElement as Page
            );
        } else if (pageDesignerStore.selectedElement.componentType === 'section') {
            res = await pageSectionApi.updatePageSectionLayout(
                pageDesignerStore.selectedElement.code,
                pageDesignerStore.selectedElement as PageSection
            );
        } else {
            res = await cellApi.updatePageCellLayout(
                pageDesignerStore.selectedElement.code,
                pageDesignerStore.selectedElement as PageCell
            );
        }

        if (res && res.code === 200) {
            feedback.success('更新样式成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
        } else {
            feedback.error('更新样式失败：' + res.msg);
        }
    };

    // 获取小驼峰的元素类型
    const getLowerCaseElementType = () => {
        switch (pageDesignerStore.selectedElementType) {
            case 'Desktop':
                return ENTITY.DESKTOP;
            case 'Page':
                return ENTITY.PAGE;
            case 'PageSection':
                return ENTITY.PAGE_SECTION;
            case 'Cell':
                return ENTITY.PAGE_CELL;
        }
    };

    // 取消批量编辑模式
    const onClickCancelBatchEditMode = () => {
        pageDesignerStore.batchEditMode = false;
        pageDesignerStore.batchEditCells = [];
    };

    // 确认批量编辑组件样式
    const onClickConfirmBatchEdit = async () => {
        await pageDesignerStore.batchUpdateCellLayoutProps(
            selectedComponentStyleCode.value,
            pageDesignerStore.batchEditCells,
            elementProps.value
        );
        onClickCancelBatchEditMode();
    };

    // 监听当前选择的元素
    watch(
        () => pageDesignerStore.selectedElement,
        (newValue) => {
            if (newValue) {
                elementProps.value = newValue.layout?.props || {};
                selectedComponentStyleCode.value = newValue.componentStyleCode;
            } else {
                elementProps.value = {};
            }
        },
        { immediate: true, deep: true }
    );

    // 组件挂载时初始化
    onMounted(() => {
        if (pageDesignerStore.selectedElement) {
            elementProps.value = pageDesignerStore.selectedElement.layout?.props || {};
        }
    });
</script>
